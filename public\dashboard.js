// Dashboard JavaScript
class FiveMDashboard {
    constructor() {
        this.baseUrl = window.location.origin;
        this.autoRefreshInterval = null;
        this.currentLogType = null;
        this.init();
    }

    init() {
        this.checkServerStatus();
        this.loadStats();
        this.setupAutoRefresh();
        
        // Load all logs on startup
        setTimeout(() => {
            this.loadAllLogs();
        }, 1000);
    }

    async checkServerStatus() {
        try {
            const response = await fetch(`${this.baseUrl}/health`);
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('serverStatus').innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            } else {
                document.getElementById('serverStatus').innerHTML = '<i class="fas fa-times-circle text-danger"></i>';
            }
        } catch (error) {
            document.getElementById('serverStatus').innerHTML = '<i class="fas fa-times-circle text-danger"></i>';
            console.error('Server status check failed:', error);
        }
    }

    async loadStats() {
        try {
            const response = await fetch(`${this.baseUrl}/logs`);
            const data = await response.json();
            
            if (data.success) {
                const logs = data.logs;
                document.getElementById('totalWebhooks').textContent = logs.webhook?.count || 0;
                document.getElementById('totalPlaytime').textContent = logs.playtime?.count || 0;
                document.getElementById('totalRichest').textContent = logs.richest?.count || 0;
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }

    async loadLogs(type) {
        this.currentLogType = type;
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.baseUrl}/logs/${type}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayLogs(data.logs, type);
                document.getElementById('logCount').textContent = `${data.logs.length} logs (Total: ${data.total})`;
            } else {
                this.showError(`Failed to load ${type} logs: ${data.message}`);
            }
        } catch (error) {
            this.showError(`Error loading ${type} logs: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    async loadAllLogs() {
        this.currentLogType = 'all';
        this.showLoading(true);
        
        try {
            const response = await fetch(`${this.baseUrl}/logs`);
            const data = await response.json();
            
            if (data.success) {
                this.displayAllLogs(data.logs);
                
                // Count total logs
                let totalCount = 0;
                Object.values(data.logs).forEach(logType => {
                    totalCount += logType.latest?.length || 0;
                });
                document.getElementById('logCount').textContent = `${totalCount} recent logs`;
            } else {
                this.showError(`Failed to load logs: ${data.message}`);
            }
        } catch (error) {
            this.showError(`Error loading logs: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    displayLogs(logs, type) {
        const container = document.getElementById('logsContainer');
        
        if (!logs || logs.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No ${type} logs found</p>
                </div>
            `;
            return;
        }

        const logsHtml = logs.reverse().map(log => this.createLogEntry(log, type)).join('');
        container.innerHTML = logsHtml;
        this.updateLastUpdate();
    }

    displayAllLogs(logsData) {
        const container = document.getElementById('logsContainer');
        let allLogs = [];
        
        // Combine all logs with type information
        Object.entries(logsData).forEach(([type, data]) => {
            if (data.latest) {
                data.latest.forEach(log => {
                    allLogs.push({...log, logType: type});
                });
            }
        });
        
        // Sort by timestamp (newest first)
        allLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        if (allLogs.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No logs found</p>
                </div>
            `;
            return;
        }

        const logsHtml = allLogs.map(log => this.createLogEntry(log, log.logType)).join('');
        container.innerHTML = logsHtml;
        this.updateLastUpdate();
    }

    createLogEntry(log, type) {
        const timestamp = new Date(log.timestamp).toLocaleString('vi-VN');
        const typeIcon = this.getTypeIcon(type);
        const typeBadge = this.getTypeBadge(type);
        
        return `
            <div class="log-entry">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <span class="badge ${typeBadge} me-2">${typeIcon} ${type.toUpperCase()}</span>
                        <strong>${this.escapeHtml(log.username || 'Unknown')}</strong>
                    </div>
                    <small class="timestamp">${timestamp}</small>
                </div>
                ${log.server_name ? `<div class="server-name mb-2">Server: ${this.escapeHtml(log.server_name)}</div>` : ''}
                <div class="content">${this.escapeHtml(log.content || 'No content')}</div>
            </div>
        `;
    }

    getTypeIcon(type) {
        const icons = {
            webhook: 'fas fa-bell',
            playtime: 'fas fa-clock',
            richest: 'fas fa-dollar-sign'
        };
        return `<i class="${icons[type] || 'fas fa-file'}"></i>`;
    }

    getTypeBadge(type) {
        const badges = {
            webhook: 'bg-primary',
            playtime: 'bg-success',
            richest: 'bg-warning'
        };
        return badges[type] || 'bg-secondary';
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        loading.style.display = show ? 'block' : 'none';
    }

    showError(message) {
        const container = document.getElementById('logsContainer');
        container.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }

    clearDisplay() {
        const container = document.getElementById('logsContainer');
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>Click on a button above to load logs</p>
            </div>
        `;
        document.getElementById('logCount').textContent = '0 logs';
        this.currentLogType = null;
    }

    setupAutoRefresh() {
        const checkbox = document.getElementById('autoRefresh');
        
        const startAutoRefresh = () => {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
            }
            
            this.autoRefreshInterval = setInterval(() => {
                this.checkServerStatus();
                this.loadStats();
                
                if (this.currentLogType) {
                    if (this.currentLogType === 'all') {
                        this.loadAllLogs();
                    } else {
                        this.loadLogs(this.currentLogType);
                    }
                }
            }, 30000); // 30 seconds
        };

        const stopAutoRefresh = () => {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
        };

        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        // Start auto refresh if checkbox is checked
        if (checkbox.checked) {
            startAutoRefresh();
        }
    }

    updateLastUpdate() {
        const now = new Date().toLocaleString('vi-VN');
        document.getElementById('lastUpdate').textContent = `Last update: ${now}`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global functions for button clicks
function loadLogs(type) {
    dashboard.loadLogs(type);
}

function loadAllLogs() {
    dashboard.loadAllLogs();
}

function clearDisplay() {
    dashboard.clearDisplay();
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new FiveMDashboard();
});
