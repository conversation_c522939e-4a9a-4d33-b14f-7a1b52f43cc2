// Script to create test data for the dashboard
const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir);
}

// Create test data
const testData = {
    webhook: [
        {
            timestamp: new Date().toISOString(),
            username: "Online Players Since Server Start",
            content: "Tổng số: 8\n1. <PERSON><PERSON><PERSON> (steam:***************) - Tổng thời gian: 02:45:30\n2. <PERSON>ran <PERSON>hi B (steam:***************) - Tổng thời gian: 01:32:15\n3. <PERSON> (steam:***************) - Tổng thời gian: 03:20:45\n4. <PERSON>am <PERSON>hi <PERSON> (steam:***************) - Tổng thời gian: 00:58:20\n5. <PERSON><PERSON> (steam:***************) - T<PERSON>ng thời gian: 02:15:10\n6. <PERSON>u Thi <PERSON> (steam:***************) - Tổng thời gian: 01:45:35\n7. <PERSON> (steam:***************) - Tổng thời gian: 00:42:25\n8. Bui Thi H (steam:***************) - Tổng thời gian: 01:18:50",
            server_name: "KaitoMC GTA V Server"
        },
        {
            timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
            username: "Server Status",
            content: "Server đang hoạt động bình thường\nSố người chơi online: 8/64\nUptime: 2h 45m\nTPS: 19.8/20.0",
            server_name: "KaitoMC GTA V Server"
        }
    ],
    
    richest: [
        {
            timestamp: new Date().toISOString(),
            username: "Top Richest",
            content: "Top 20 nhiều tiền mặt nhất:\n1. Nguyen Van A (steam:***************) - $1500000\n2. Tran Thi B (steam:***************) - $1200000\n3. Le Van C (steam:***************) - $1000000\n4. Pham Thi D (steam:***************) - $800000\n5. Hoang Van E (steam:***************) - $600000\n6. Vu Thi F (steam:***************) - $500000\n7. Do Van G (steam:***************) - $450000\n8. Bui Thi H (steam:***************) - $400000\n9. Dang Van I (steam:***************) - $350000\n10. Cao Thi J (steam:***************) - $300000\n\nTop 20 nhiều tiền bank nhất:\n1. Nguyen Van A (steam:***************) - $5000000\n2. Tran Thi B (steam:***************) - $4500000\n3. Le Van C (steam:***************) - $4000000\n4. Pham Thi D (steam:***************) - $3500000\n5. Hoang Van E (steam:***************) - $3000000\n6. Vu Thi F (steam:***************) - $2500000\n7. Do Van G (steam:***************) - $2000000\n8. Bui Thi H (steam:***************) - $1800000\n9. Dang Van I (steam:***************) - $1600000\n10. Cao Thi J (steam:***************) - $1400000",
            server_name: "KaitoMC GTA V Server",
            type: "richest"
        }
    ],
    
    playtime: [
        {
            timestamp: new Date().toISOString(),
            username: "Tổng Playtime",
            content: "Tổng số người: 25\n1. Nguyen Van A (steam:***************) - 156h 23m\n2. Tran Thi B (steam:***************) - 142h 15m\n3. Le Van C (steam:***************) - 128h 45m\n4. Pham Thi D (steam:***************) - 115h 30m\n5. Hoang Van E (steam:***************) - 98h 12m\n6. Vu Thi F (steam:***************) - 87h 45m\n7. Do Van G (steam:***************) - 76h 20m\n8. Bui Thi H (steam:***************) - 65h 35m\n9. Dang Van I (steam:***************) - 54h 18m\n10. Cao Thi J (steam:***************) - 43h 25m\n11. Mai Van K (steam:110000123456799) - 38h 12m\n12. Ly Thi L (steam:110000123456800) - 32h 45m\n13. Truong Van M (steam:110000123456801) - 28h 30m\n14. Ngo Thi N (steam:110000123456802) - 24h 15m\n15. Phan Van O (steam:110000123456803) - 20h 40m\n16. Dinh Thi P (steam:110000123456804) - 18h 25m\n17. Lam Van Q (steam:110000123456805) - 15h 50m\n18. Vo Thi R (steam:110000123456806) - 12h 35m\n19. Ta Van S (steam:110000123456807) - 10h 20m\n20. Duong Thi T (steam:110000123456808) - 8h 45m",
            server_name: "KaitoMC GTA V Server",
            type: "playtime"
        }
    ],

    topcoin: [
        {
            timestamp: new Date().toISOString(),
            username: "Top Coin",
            content: "Top 20 người có nhiều coin nhất:\n1. Dragon King (steam:***************) - 15000 coin\n2. Coin Master (steam:***************) - 12500 coin\n3. Rich Player (steam:***************) - 10000 coin\n4. Money Boss (steam:***************) - 8500 coin\n5. Coin Hunter (steam:***************) - 7200 coin\n6. Gold Digger (steam:***************) - 6800 coin\n7. Cash King (steam:***************) - 5900 coin\n8. Coin Collector (steam:***************) - 5400 coin\n9. Money Maker (steam:***************) - 4800 coin\n10. Rich Gamer (steam:***************) - 4200 coin\n11. Coin Lord (steam:110000123456799) - 3800 coin\n12. Money Hunter (steam:110000123456800) - 3400 coin\n13. Cash Master (steam:110000123456801) - 3000 coin\n14. Coin Pro (steam:110000123456802) - 2600 coin\n15. Rich Boy (steam:110000123456803) - 2200 coin\n16. Money Pro (steam:110000123456804) - 1800 coin\n17. Coin Expert (steam:110000123456805) - 1500 coin\n18. Cash Pro (steam:110000123456806) - 1200 coin\n19. Money Fan (steam:110000123456807) - 1000 coin\n20. Coin Newbie (steam:110000123456808) - 800 coin",
            server_name: "Dragon City Server",
            type: "topcoin"
        }
    ]
};

// Write test data to files
Object.keys(testData).forEach(type => {
    const filePath = path.join(logsDir, `${type}.json`);
    fs.writeFileSync(filePath, JSON.stringify(testData[type], null, 2));
    console.log(`✅ Created ${type}.json with ${testData[type].length} entries`);
});

console.log('\n🎉 Test data created successfully!');
console.log('📊 Dashboard should now display data at: https://gtav.kaitomc.site/');
console.log('\n📁 Files created:');
console.log('- logs/webhook.json');
console.log('- logs/richest.json');
console.log('- logs/playtime.json');
