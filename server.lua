if Boutique.Framework == "esx" then
    ESX = nil
    Citizen.CreateThread(function()
        while ESX == nil do
            TriggerEvent(Boutique.getSharedObject, function(obj) ESX = obj end)
            Citizen.Wait(500)
        end
    end)
elseif Boutique.Framework == "newEsx" then
    ESX = exports[Boutique.Extended_Name]:getSharedObject()
end

if (Boutique.Framework == "esx" or Boutique.Framework == "newEsx") then
    AddEventHandler('esx:playerLoaded', function(source)
        local identifier = GetIdentifier(source)

        if not ESX then
            print("Error: ESX is not initialized")
            return
        end

        local xPlayer = ESX.GetPlayerFromId(source)
    
        if identifier then
            local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {
                ['@citizenID'] = identifier
            })   
            if not data or not data[1] then
                ExecuteSql('INSERT INTO boutique (citizenID, boutique_code, points) VALUES (@citizenID, @boutique_code, @points)', {
                    ['@citizenID'] = identifier,
                    ['@boutique_code'] = xPlayer.UniqueID,
                    ['@points'] = Boutique.NewArrivalCredit
                })
            end
        end
    end)
else
    AddEventHandler('playerConnecting', function()
        local source = source
        local identifier = GetIdentifier(source)

        if not ESX then
            print("Error: ESX is not initialized")
            return
        end

        local xPlayer = ESX.GetPlayerFromId(source)

        if identifier then
            local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {
                ['@citizenID'] = identifier
            })   
            if not data or not data[1] then
                ExecuteSql('INSERT INTO boutique (citizenID, boutique_code, points) VALUES (@citizenID, @boutique_code, @points)', {
                    ['@citizenID'] = identifier,
                    ['@boutique_code'] = xPlayer.UniqueID,
                    ['@points'] = Boutique.NewArrivalCredit
                })
                
            end
        end
    end)
end

RegisterNetEvent("boutique:setPlayerBucket", function(bucket)
    local src = source
    SetPlayerRoutingBucket(src, bucket)
end)

function GetPlayerCredits(source, callback)
    local source = source
    local identifier = GetIdentifier(source)

    if identifier then
        local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {
            ['@citizenID'] = identifier
        })
        if data and data[1] then   
            callback(data[1].points)
        end
    else
    end
end

RegisterNetEvent("GetPlayerCredits")
AddEventHandler("GetPlayerCredits", function()
    local source = source

    if not ESX then
        print("Error: ESX is not initialized")
        return
    end

    GetPlayerCredits(source, function(credits)
        local numCredits = tonumber(credits)
        TriggerClientEvent("ReceivePlayerCredit", source, numCredits)
    end)
end)

RegisterNetEvent("GetCodeBoutique")
AddEventHandler("GetCodeBoutique", function()
    local source = source
    local identifier = GetIdentifier(source)

    if not ESX then
        print("Error: ESX is not initialized")
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)

    if identifier then
        local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {
            ['@citizenID'] = identifier
        })
        if data and data[1] then   
            TriggerClientEvent("ReceiveBoutiqueId", source, xPlayer.UniqueID)
        end
    else
    end
end)



RegisterServerEvent("DeleteCredits")
AddEventHandler("DeleteCredits", function(source, amount)
    local source = source
    local identifier = GetIdentifier(source)

    if identifier then
        local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {
            ['@citizenID'] = identifier
        })
        if data and data[1] then
            local CurrentAmount = data[1].points
            local NewAmount = CurrentAmount - amount

            GetPlayerCredits(source, function(credits)
                local numCredits = tonumber(credits)
                local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE citizenId = @citizenId", {
                    ['@citizenId'] = identifier,
                    ['@points'] = NewAmount 
                })
                TriggerClientEvent("ReceivePlayerCredit", source, NewAmount)
            end)
        else
        end
    else
    end
end)

RegisterServerEvent("Boutique:giveCoins")
AddEventHandler("Boutique:giveCoins", function(point, uniqueid)
    local source = source
    local identifier = GetIdentifier(source)

    if identifier then
        local result = ExecuteSql("SELECT * FROM boutique WHERE citizenID = @citizenID", {
            ['@citizenID'] = identifier
        })

        point = tonumber(point)

        if point and result and result[1] and result[1].points >= point then
            local NewAmount = result[1].points - point
            local result2 = ExecuteSql("SELECT * FROM boutique WHERE boutique_code = @boutique_code", {
                ['@boutique_code'] = uniqueid
            })

            if result2 and result2[1] then
                local receiver_identifier = result2[1].citizenID
                if receiver_identifier == identifier then
                    TriggerClientEvent("b:Notify", source, Boutique.Notifications.GiveCredits.RefuseIfYour)
                    return
                end

                ExecuteSql("UPDATE boutique SET points = @points WHERE citizenID = @citizenID", {
                    ['@citizenID'] = identifier,
                    ['@points'] = NewAmount
                })
                GetPlayerCredits(source, function(credits)
                    local numCredits = tonumber(credits)
                    TriggerClientEvent("ReceivePlayerCredit", source, numCredits) 
                end)

                local addpoint = result2[1].points + point
                local xTargetSource = GetPlayerFromIdentifier(receiver_identifier)

                ExecuteSql("UPDATE boutique SET points = @points WHERE boutique_code = @boutique_code", {
                    ['@boutique_code'] = uniqueid,
                    ['@points'] = addpoint
                })

                TriggerClientEvent("b:Notify", xTargetSource, Boutique.Notifications.GiveCredits.YouHaveReceive.." " .. point .. " "..Boutique.Notifications.GiveCredits.YouHaveReceive2.. " " .. GetPlayerName(source))
                
                GetPlayerCredits(xTargetSource, function(credits)
                    local numCredits = tonumber(credits)
                    TriggerClientEvent("ReceivePlayerCredit", xTargetSource, numCredits) 
                end)

                TriggerClientEvent("b:Notify", source, Boutique.Notifications.GiveCredits.TransferGood)
            else
                TriggerClientEvent("b:Notify", source, Boutique.Notifications.GiveCredits.TransferProblem)
            end
        else
            TriggerClientEvent("b:Notify", source, Boutique.Notifications.GiveCredits.TransferProblem)
        end
    end
end)

RegisterNetEvent("Boutique:Rewards")
AddEventHandler("Boutique:Rewards", function(type, data)
    if data == nil then
        return
    end

    local _source = source
    local citizenId = GetIdentifier(_source)
    local coinsToSubtract = data.coins or 0
    local name = data.name or ""
    local model = data.model or ""
    local count = data.count or 0
    local lootbox = data.lootbox
    local selling = data.selling

    local xPlayer
    if Boutique.Framework == "esx" or Boutique.Framework == "newEsx" then
        if not ESX then
            print("Error: ESX is not initialized")
            return
        end
        xPlayer = ESX.GetPlayerFromId(_source)
        if xPlayer == nil then
            return
        end
    end


    GetPlayerCredits(_source, function(credits)
        local numCredits = tonumber(credits)
        if numCredits >= coinsToSubtract then 
    
            -- Achat de véhicule
            if type == "vehicle" then
                -- Lấy amount từ config
                local amount = 1
                for _, v in ipairs(Boutique.Vehicles or {}) do
                    if v.model == model then
                        amount = v.amount or 1
                        break
                    end
                end
                for i = 1, amount do
                    local plate = GeneratePlate()
                    local vehicleData = {model = GetHashKey(model), plate = plate, fuelLevel = 100.0}
                    GiveVehicle(_source, citizenId, plate, vehicleData)

                    local dataText
                    if lootbox then
                        dataText = "a gagné un véhicule " .. model .. " depuis une caisse"
                    else
                        dataText = "a acheté un véhicule " .. model .. " với la plaque " .. plate .. " pour " .. coinsToSubtract .. " coins"
                    end
    
                    ExecuteSql("INSERT INTO boutique_history (uniqueid, data, lot) VALUES (@uniqueid, @data, @lot)", {
                        ['@uniqueid'] = xPlayer.UniqueID,
                        ['@data'] = dataText,
                        ['@lot'] = model
                    })
    
                    -- Envoi à Discord
                    if lootbox then
                        SendToDiscord("lootbox", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa nhận được "..model.." từ rương quay!")
                    else
                        SendToDiscord("vehicle", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa mua xe "..model.." tại cửa hàng!")
                    end
                    -- Gửi thông tin xe về client để spawn xe chính chủ
                    TriggerClientEvent('nvboutique:spawnOwnedVehicle', _source, vehicleData)
                end
    
            elseif type == "item" then
                local itemName = data.item or data.name
                local found = false
                for _, v in ipairs(Boutique.Items or {}) do
                    if v.item == itemName then
                        GiveItem(_source, v.item, v.amount or 1)
                        local dataText = "a acheté l'item " .. v.item
                        ExecuteSql("INSERT INTO boutique_history (uniqueid, data, lot) VALUES (@uniqueid, @data, @lot)", {
                            ['@uniqueid'] = xPlayer.UniqueID,
                            ['@data'] = dataText,
                            ['@lot'] = v.item
                        })
                        if lootbox then
                            SendToDiscord("lootbox", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa nhận được "..v.item.." từ rương quay!")
                        else
                            SendToDiscord("item", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa mua vật phẩm "..v.item.." tại cửa hàng!")
                        end
                        found = true
                        break
                    end
                end
                if not found then
                    print("[Boutique] Không tìm thấy item: "..itemName)
                end

            elseif type == "weapon" then
                local itemName = data.item or data.name
                local found = false
                for _, v in ipairs(Boutique.Weapons or {}) do
                    if v.item == itemName then
                        GiveItem(_source, v.item, v.amount or 1)
                        local dataText = "a acheté l'item " .. v.item
                        ExecuteSql("INSERT INTO boutique_history (uniqueid, data, lot) VALUES (@uniqueid, @data, @lot)", {
                            ['@uniqueid'] = xPlayer.UniqueID,
                            ['@data'] = dataText,
                            ['@lot'] = v.item
                        })
                        if lootbox then
                            SendToDiscord("lootbox", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa nhận được "..v.item.." từ rương quay!")
                        else
                            SendToDiscord("item", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa mua vật phẩm "..v.item.." tại cửa hàng!")
                        end
                        found = true
                        break
                    end
                end
                if not found then
                    -- fallback: cấp item đầu tiên hoặc báo lỗi
                    if Boutique.Weapons and #Boutique.Weapons > 0 then
                        local v = Boutique.Weapons[1]
                        GiveItem(_source, v.item, v.amount or 1)
                    end
                end
    
            elseif type == "money" then
                local itemName = data.item
                local found = false
                for _, v in ipairs(Boutique.Money or {}) do
                    if v.item == itemName then
                        GiveItem(_source, v.item, v.amount or 1)
                        local dataText = "a acheté l'item " .. v.item
                        ExecuteSql("INSERT INTO boutique_history (uniqueid, data, lot) VALUES (@uniqueid, @data, @lot)", {
                            ['@uniqueid'] = xPlayer.UniqueID,
                            ['@data'] = dataText,
                            ['@lot'] = v.item
                        })
                        if lootbox then
                            SendToDiscord("lootbox", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa nhận được "..v.item.." từ rương quay!")
                        else
                            SendToDiscord("item", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa mua vật phẩm "..v.item.." tại cửa hàng!")
                        end
                        found = true
                        break
                    end
                end
                if not found then
                    -- fallback: cấp item đầu tiên hoặc báo lỗi
                    if Boutique.Money and #Boutique.Money > 0 then
                        local v = Boutique.Money[1]
                        GiveItem(_source, v.item, v.amount or 1)
                    end
                end
    

            elseif type == "coins" then
                local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {['@citizenID'] = citizenId})
                if data and data[1] then
                    local CurrentAmount = data[1].points
                    local NewAmount = CurrentAmount + count
                    local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE citizenId = @citizenId", {
                        ['@citizenId'] = citizenId,
                        ['@points'] = NewAmount
                    })
    
                    local dataText = "a acheté " .. count .. " crédits"
                    ExecuteSql("INSERT INTO boutique_history (uniqueid, data, lot) VALUES (@uniqueid, @data, @lot)", {
                        ['@uniqueid'] = xPlayer.UniqueID,
                        ['@data'] = dataText,
                        ['@lot'] = count
                    })
    
                    SendToDiscord("coins", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa nhận được "..count.." xu từ rương!")
                    if exec then
                        TriggerClientEvent("ReceivePlayerCredit", _source, NewAmount)
                    end
                end
                if selling then
                    SendToDiscord("selling", "Tên: ``"..GetPlayerName(_source).."``\nID: ``"..citizenId.."``\nVừa bán vật phẩm từ rương và nhận được "..count.." xu!")
                end
            end
    
            TriggerEvent("DeleteCredits", _source, coinsToSubtract)
        else
            TriggerClientEvent("b:Notify", _source, Boutique.Notifications.NoCoins)
        end
    end)
    
end)

RegisterCommand(Boutique.GiveCmdName, function(source, args, raw)
    local uniqueid = args[1]
    local point = args[2]

    if uniqueid == nil or point == nil then
        print("Usage : "..Boutique.GiveCmdName.." [id unique] [nbPoint]")
        return
    end

    if source == 0 then 
        local result = ExecuteSql("SELECT * FROM boutique WHERE boutique_code = @boutique_code", {
            ['@boutique_code'] = uniqueid
        })

        if result and result[1] then
            local CurrentAmount = result[1].points
            local NewAmount = tonumber(CurrentAmount) + tonumber(point)

            local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE boutique_code = @boutique_code", {
                ['@boutique_code'] = uniqueid,
                ['@points'] = NewAmount 
            })

            if exec then
                print("Vous venez de donner " .. tostring(point) .. " à l'id boutique : " .. tostring(uniqueid).."\nIl possède maintenant "..tostring(NewAmount).." Points boutique")
                SendToDiscord("givepoint", "Console vừa cộng thêm ".. tostring(point) .." xu cho ID boutique: ".. tostring(uniqueid) .."\nTổng xu hiện tại: "..tostring(NewAmount))

            else
                print("Erreur lors de la mise à jour de la base de données")
            end

        else
            print("Erreur: Aucune donnée retournée de la requête SQL")
        end
    else
        print("La commande n'est pas autorisé ici!")
    end

end, false)

RegisterCommand('addcoin', function(source, args, raw)
    if source == 0 then
       -- print("Lệnh này chỉ dùng trong game!")
        return
    end
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer or (xPlayer.getGroup and (xPlayer.getGroup() ~= 'admin' and xPlayer.getGroup() ~= 'superadmin')) then
        TriggerClientEvent('b:Notify', source, '~r~Bạn không có quyền sử dụng lệnh này!')
        return
    end
    local targetId = tonumber(args[1])
    local amount = tonumber(args[2])
    if not targetId or not amount or amount <= 0 then
        TriggerClientEvent('b:Notify', source, 'Cú pháp: /addcoin [ID người chơi] [số coin]')
        return
    end
    local targetPlayer = ESX.GetPlayerFromId(targetId)
    if not targetPlayer then
        TriggerClientEvent('b:Notify', source, 'Không tìm thấy người chơi!')
        return
    end
    local identifier = GetIdentifier(targetId)
    if not identifier then
        TriggerClientEvent('b:Notify', source, 'Không lấy được identifier!')
        return
    end
    local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {['@citizenID'] = identifier})
    if data and data[1] then
        local CurrentAmount = data[1].points
        local NewAmount = CurrentAmount + amount
        local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE citizenId = @citizenId", {
            ['@citizenId'] = identifier,
            ['@points'] = NewAmount
        })
        if exec then
            TriggerClientEvent('b:Notify', source, 'Đã cộng '..amount..' coin cho ID '..targetId)
            TriggerClientEvent('b:Notify', targetId, 'Bạn vừa được admin cộng '..amount..' coin!')
            TriggerClientEvent("ReceivePlayerCredit", targetId, NewAmount)
        else
            TriggerClientEvent('b:Notify', source, 'Có lỗi khi cập nhật database!')
        end
    else
        TriggerClientEvent('b:Notify', source, 'Người chơi chưa từng vào server hoặc chưa có dữ liệu boutique!')
    end
end, false)

RegisterCommand('addallcoin', function(source, args, raw)
    if source ~= 0 then
        local xPlayer = ESX.GetPlayerFromId(source)
        if not xPlayer or (xPlayer.getGroup and (xPlayer.getGroup() ~= 'admin' and xPlayer.getGroup() ~= 'superadmin')) then
            TriggerClientEvent('b:Notify', source, '~r~Bạn không có quyền sử dụng lệnh này!')
            return
        end
    end
    local amount = tonumber(args[1])
    if not amount or amount <= 0 then
        if source ~= 0 then
            TriggerClientEvent('b:Notify', source, 'Cú pháp: /addallcoin [số coin]')
        else
            print('Cú pháp: /addallcoin [số coin]')
        end
        return
    end
    for _, playerId in ipairs(GetPlayers()) do
        local identifier = GetIdentifier(tonumber(playerId))
        if identifier then
            local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {['@citizenID'] = identifier})
            if data and data[1] then
                local CurrentAmount = data[1].points
                local NewAmount = CurrentAmount + amount
                local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE citizenId = @citizenId", {
                    ['@citizenId'] = identifier,
                    ['@points'] = NewAmount
                })
                if exec then
                    TriggerClientEvent("ReceivePlayerCredit", tonumber(playerId), NewAmount)
                    TriggerClientEvent('b:Notify', tonumber(playerId), '~g~Bạn vừa nhận được '..amount..' xu từ admin!')
                end
            end
        end
    end
    if source ~= 0 then
        TriggerClientEvent('b:Notify', source, '~g~Đã cộng coin cho tất cả người chơi online!')
    else
        print('Đã cộng coin cho tất cả người chơi online!')
    end
end, false)

local NumberCharset = {}
local Charset = {}

for i = 48,  57 do table.insert(NumberCharset, string.char(i)) end
for i = 65,  90 do table.insert(Charset, string.char(i)) end
for i = 97, 122 do table.insert(Charset, string.char(i)) end

function GeneratePlate()
    local generatedPlate
    local doBreak = false

    while true do
        Citizen.Wait(2)
        math.randomseed(GetGameTimer())
        generatedPlate = GenerateFormattedPlate(Boutique.PlateFormat)
        tablesql = Boutique.TableNameVehicle

        local result = ExecuteSql("SELECT 1 FROM "..tablesql.." WHERE plate = '"..generatedPlate.."'")
        if result[1] == nil then 
            doBreak = true
        end

        if doBreak then
            break
        end
    end

    return generatedPlate
end

function GenerateFormattedPlate(format)
    local plate = ""
    for i = 1, #format do
        local char = format:sub(i, i)
        if char == "L" then
            plate = plate .. string.upper(GetRandomLetter(1)) 
        elseif char == "N" then
            plate = plate .. GetRandomNumber(1)
        end
    end
    return plate
end

function GetRandomNumber(length)
	Citizen.Wait(1)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomNumber(length - 1) .. NumberCharset[math.random(1, #NumberCharset)]
	else
		return ''
	end
end

function GetRandomLetter(length)
	Citizen.Wait(1)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomLetter(length - 1) .. string.upper(Charset[math.random(1, #Charset)]) -- Conversion en majuscules
	else
		return ''
	end
end

function ExecuteSql(query, params)
    local IsBusy = true
    local result = nil
    if Boutique.Mysql == "oxmysql" then
        if MySQL == nil then
            exports.oxmysql:execute(query, params, function(data)
                result = data
                IsBusy = false
            end)
        else
            MySQL.query(query, params, function(data)
                result = data
                IsBusy = false
            end)
        end
    elseif Boutique.Mysql == "ghmattimysql" then
        exports.ghmattimysql:execute(query, params, function(data)
            result = data
            IsBusy = false
        end)
    elseif Boutique.Mysql == "mysql-async" then   
        MySQL.Async.fetchAll(query, params, function(data)
            result = data
            IsBusy = false
        end)
    end
    while IsBusy do
        Citizen.Wait(0)
    end
    return result
end

RegisterNetEvent('nvboutique:requestData')
AddEventHandler('nvboutique:requestData', function()
    local src = source
    TriggerClientEvent('nvboutique:receiveData', src, Boutique.Vehicles, Boutique.Weapons, Boutique.Money, Boutique.Tebex, Boutique.Cases, Boutique.Name)
end)

RegisterNetEvent('Boutique:DiscordAddCoin')
AddEventHandler('Boutique:DiscordAddCoin', function(targetId, amount)
    local identifier = GetIdentifier(targetId)
    if not identifier then
        print('[Boutique:DiscordAddCoin] Không lấy được identifier!')
        return
    end
    local data = ExecuteSql('SELECT * FROM boutique WHERE citizenID = @citizenID', {['@citizenID'] = identifier})
    if data and data[1] then
        local CurrentAmount = data[1].points
        local NewAmount = CurrentAmount + amount
        local exec = ExecuteSql("UPDATE `boutique` SET `points`= @points WHERE citizenID = @citizenID", {
            ['@citizenID'] = identifier,
            ['@points'] = NewAmount
        })
        if exec then
            print('[Boutique:DiscordAddCoin] Đã cộng '..amount..' coin cho ID '..targetId)
            TriggerClientEvent('b:Notify', targetId, 'Bạn vừa được admin cộng '..amount..' coin từ Discord!')
            TriggerClientEvent("ReceivePlayerCredit", targetId, NewAmount)
        else
            print('[Boutique:DiscordAddCoin] Có lỗi khi cập nhật database!')
        end
    else
        print('[Boutique:DiscordAddCoin] Người chơi chưa từng vào server hoặc chưa có dữ liệu boutique!')
    end
end)

-- Hàm gửi top coin đến web dashboard
function SendTopCoinToWeb()
    local data = ExecuteSql('SELECT * FROM boutique ORDER BY points DESC LIMIT 20', {})
    if data and #data > 0 then
        local msg = 'Top 20 người có nhiều coin nhất:\n'
        for i, v in ipairs(data) do
            local name = v.citizenID or 'Không rõ'

            -- Thử lấy tên từ người chơi online
            for _, playerId in ipairs(GetPlayers()) do
                local playerIdentifier = GetIdentifier(tonumber(playerId))
                if playerIdentifier == v.citizenID then
                    name = GetPlayerName(tonumber(playerId))
                    break
                end
            end

            -- Nếu vẫn không có tên, thử lấy từ database users
            if name == v.citizenID then
                local userQuery = 'SELECT firstname, lastname FROM users WHERE identifier = @identifier LIMIT 1'
                local userData = ExecuteSql(userQuery, {['@identifier'] = v.citizenID})
                if userData and userData[1] then
                    if userData[1].firstname and userData[1].lastname then
                        name = userData[1].firstname .. ' ' .. userData[1].lastname
                    elseif userData[1].firstname then
                        name = userData[1].firstname
                    end
                end
            end

            -- Nếu tên quá dài, cắt bớt
            if string.len(name) > 20 then
                name = string.sub(name, 1, 17) .. "..."
            end

            msg = msg .. string.format('%d. %s (%s) - %s coin\n', i, name, v.citizenID, v.points)
        end

        -- Gửi đến web dashboard
        local webhook = "https://gtav.kaitomc.site/topcoin"
        PerformHttpRequest(webhook, function(err, text, headers)
            if err == 200 then
                print("✅ Top coin sent to web dashboard successfully")
            else
                print("❌ Top coin web dashboard failed: " .. tostring(err) .. " - " .. tostring(text))
            end
        end, 'POST', json.encode({
            username = "Top Coin",
            content = msg,
            timestamp = os.time(),
            server_name = GetConvar("sv_hostname", "Dragon City Server")
        }), {
            ['Content-Type'] = 'application/json; charset=utf-8',
            ['Accept-Charset'] = 'utf-8'
        })

        -- Vẫn gửi Discord như cũ
        local discordMsg = ''
        for i, v in ipairs(data) do
            local name = v.name or v.citizenID or 'Không rõ'
            discordMsg = discordMsg .. string.format('**%d.** %s (ID: `%s`) - **%s xu**\n', i, name, v.citizenID, v.points)
        end
        SendToDiscord('topcoin', 'TOP 10 COIN SERVER', discordMsg)
    else
        -- Gửi thông báo không có dữ liệu
        local webhook = "https://gtav.kaitomc.site/topcoin"
        PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({
            username = "Top Coin",
            content = "Không có dữ liệu người chơi!",
            timestamp = os.time(),
            server_name = GetConvar("sv_hostname", "Dragon City Server")
        }), { ['Content-Type'] = 'application/json' })

        SendToDiscord('topcoin', 'TOP 10 COIN SERVER', 'Không có dữ liệu người chơi!')
    end
end

-- Thread tự động gửi top coin mỗi 30 phút
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1800000) -- 30 phút (30 * 60 * 1000 ms)
        SendTopCoinToWeb()
    end
end)

-- Command để gửi top coin ngay lập tức
RegisterCommand("sendtopcoin", function(source, args, rawCommand)
    if source == 0 then -- Chỉ console mới được dùng
        SendTopCoinToWeb()
        print("✅ Top coin data sent to web dashboard!")
    else
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and (xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'superadmin') then
            SendTopCoinToWeb()
            TriggerClientEvent('b:Notify', source, '✅ Đã gửi top coin lên web dashboard!')
        else
            TriggerClientEvent('b:Notify', source, '❌ Bạn không có quyền sử dụng lệnh này!')
        end
    end
end, false)

-- Command test để gửi dữ liệu mẫu (chỉ để test)
RegisterCommand("testopcoin", function(source, args, rawCommand)
    if source == 0 then
        local testData = {
            {name = "Dragon King", citizenID = "steam:110000123456789", points = 15000},
            {name = "Coin Master", citizenID = "steam:110000123456790", points = 12500},
            {name = "Rich Player", citizenID = "steam:110000123456791", points = 10000},
            {name = "Money Boss", citizenID = "steam:110000123456792", points = 8500},
            {name = "Coin Hunter", citizenID = "steam:110000123456793", points = 7200}
        }

        local msg = 'Top 20 người có nhiều coin nhất:\n'
        for i, v in ipairs(testData) do
            msg = msg .. string.format('%d. %s (%s) - %s coin\n', i, v.name, v.citizenID, v.points)
        end

        local webhook = "https://gtav.kaitomc.site/topcoin"
        PerformHttpRequest(webhook, function(err, text, headers)
            if err == 200 then
                print("✅ Test top coin sent to web dashboard successfully")
            else
                print("❌ Test top coin failed: " .. tostring(err))
            end
        end, 'POST', json.encode({
            username = "Top Coin",
            content = msg,
            timestamp = os.time(),
            server_name = "Dragon City Server (Test)"
        }), { ['Content-Type'] = 'application/json' })
    end
end, false)