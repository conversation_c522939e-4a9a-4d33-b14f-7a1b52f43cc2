{"name": "fivem-data-receiver", "version": "1.0.0", "description": "Server endpoint để nhận dữ liệu từ FiveM script", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fivem", "webhook", "express", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}