// Script to send top coin data with special Unicode characters
const fetch = require('node-fetch');

const testData = {
    username: "Top Coin",
    content: `Top 20 người có nhiều coin nhất:
1. <PERSON> (110000158175bab) - 1868600 coin
2. <PERSON><PERSON><PERSON> (1100001171116b1) - 1285100 coin
3. <PERSON><PERSON><PERSON> (1100001482ca175) - 1187500 coin
4. <PERSON><PERSON><PERSON> (11000014f689167) - 1108200 coin
5. <PERSON><PERSON> (110000146f5dca3) - 29885 coin
6. <PERSON><PERSON> (11000013881584f) - 898 coin
7. Zzz Htp (110000149e05b5e) - 450 coin
8. <PERSON><PERSON><PERSON> (1100001528fc642) - 410 coin
9. <PERSON><PERSON><PERSON> (11000013b95d485) - 170 coin
10. <PERSON><PERSON><PERSON><PERSON> (steam:110000123456789) - 15000 coin
11. <PERSON><PERSON><PERSON><PERSON> (steam:110000123456790) - 12500 coin
12. <PERSON><PERSON> (steam:110000123456791) - 10000 coin
13. <PERSON><PERSON><PERSON> (steam:110000123456792) - 8500 coin
14. <PERSON><PERSON><PERSON> (steam:110000123456793) - 7200 coin
15. V<PERSON> <PERSON>h<PERSON> F (steam:110000123456794) - 6800 coin
16. Đỗ V<PERSON>n G (steam:110000123456795) - 5900 coin
17. Bùi Th<PERSON> H (steam:110000123456796) - 5400 coin
18. Đặng V<PERSON>n I (steam:110000123456797) - 4800 coin
19. <PERSON> Thị J (steam:110000123456798) - 4200 coin
20. Mai Văn K (steam:110000123456799) - 3800 coin`,
    timestamp: Math.floor(Date.now() / 1000),
    server_name: "Dragon City Server"
};

async function sendSpecialCharsTopCoin() {
    try {
        console.log('✨ Sending top coin data with special characters...');
        
        const response = await fetch('https://gtav.kaitomc.site/topcoin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Special characters top coin data sent successfully!');
            console.log('📊 Response:', result);
            console.log('\n🎯 Test names with special characters:');
            console.log('- Dragon (normal)');
            console.log('- Toàn Thắng (Vietnamese)');
            console.log('- McN (normal)');
            console.log('- Diễm Quỳnh (Vietnamese with special chars)');
            console.log('- Yuki Frost (normal)');
            console.log('- Chiu Liêm (Vietnamese)');
            console.log('- Bạch Béo (Vietnamese)');
            console.log('- Tài Kays (Vietnamese)');
            console.log('- And more Vietnamese names...');
        } else {
            console.log('❌ Failed to send special chars data:', result);
        }
        
    } catch (error) {
        console.error('❌ Error sending special chars data:', error.message);
    }
}

sendSpecialCharsTopCoin();
