// Test script to check FiveM API endpoints
const fetch = require('node-fetch');

const serverAddress = '**************:20002';

async function testFiveMAPI() {
    console.log('🧪 Testing FiveM API endpoints...\n');
    
    // Test info.json
    try {
        console.log('📊 Testing info.json...');
        const infoResponse = await fetch(`http://${serverAddress}/info.json`);
        const infoData = await infoResponse.json();
        console.log('✅ Info data:', {
            hostname: infoData.hostname,
            maxclients: infoData.vars?.sv_maxclients,
            gametype: infoData.vars?.gametype,
            mapname: infoData.vars?.mapname
        });
    } catch (error) {
        console.log('❌ Info API failed:', error.message);
    }
    
    console.log('');
    
    // Test dynamic.json
    try {
        console.log('⚡ Testing dynamic.json...');
        const dynamicResponse = await fetch(`http://${serverAddress}/dynamic.json`);
        const dynamicData = await dynamicResponse.json();
        console.log('✅ Dynamic data:', {
            hostname: dynamicData.hostname,
            clients: dynamicData.clients,
            sv_maxclients: dynamicData.sv_maxclients
        });
    } catch (error) {
        console.log('❌ Dynamic API failed:', error.message);
    }
    
    console.log('');
    
    // Test players.json
    try {
        console.log('👥 Testing players.json...');
        const playersResponse = await fetch(`http://${serverAddress}/players.json`);
        const playersData = await playersResponse.json();
        console.log('✅ Players data:', {
            totalPlayers: playersData.length,
            samplePlayer: playersData[0] ? {
                name: playersData[0].name,
                ping: playersData[0].ping,
                identifiers: playersData[0].identifiers?.slice(0, 2) // Show first 2 identifiers
            } : 'No players online'
        });
        
        if (playersData.length > 0) {
            console.log('📋 All online players:');
            playersData.forEach((player, index) => {
                const steamId = player.identifiers?.find(id => id.startsWith('steam:')) || 'Unknown';
                console.log(`   ${index + 1}. ${player.name} (${steamId}) - Ping: ${player.ping}ms`);
            });
        }
    } catch (error) {
        console.log('❌ Players API failed:', error.message);
    }
    
    console.log('\n🎯 Test completed!');
}

testFiveMAPI();
