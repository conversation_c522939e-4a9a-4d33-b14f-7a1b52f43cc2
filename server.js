const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// Add fetch for Node.js (if not available)
if (!global.fetch) {
    global.fetch = require('node-fetch');
}

const app = express();
const PORT = process.env.PORT || 23403;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files for dashboard
app.use(express.static(path.join(__dirname, 'public')));

// T<PERSON><PERSON> thư mục logs nếu chưa tồn tại
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir);
}

// Hàm ghi log vào file
function writeLogToFile(filename, data) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        ...data
    };
    
    const logFile = path.join(logsDir, `${filename}.json`);
    let logs = [];
    
    // <PERSON><PERSON><PERSON> logs hiện tại nếu file tồn tại
    if (fs.existsSync(logFile)) {
        try {
            const fileContent = fs.readFileSync(logFile, 'utf8');
            logs = JSON.parse(fileContent);
        } catch (error) {
            console.error('Error reading log file:', error);
            logs = [];
        }
    }
    
    // Thêm log mới
    logs.push(logEntry);
    
    // Giữ chỉ 1000 logs gần nhất
    if (logs.length > 1000) {
        logs = logs.slice(-1000);
    }
    
    // Ghi lại file
    try {
        fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));
        console.log(`✅ Log saved to ${filename}.json`);
    } catch (error) {
        console.error('Error writing log file:', error);
    }
}

// Endpoint nhận webhook chung
app.post('/webhook', (req, res) => {
    try {
        console.log('📨 Received webhook:', req.body);

        const data = {
            username: fixUTF8Encoding(req.body.username || 'Unknown'),
            content: fixUTF8Encoding(req.body.content || ''),
            timestamp: req.body.timestamp || Date.now(),
            server_name: fixUTF8Encoding(req.body.server_name || 'FiveM Server')
        };

        writeLogToFile('webhook', data);

        res.status(200).json({
            success: true,
            message: 'Webhook received successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Webhook error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint nhận dữ liệu playtime
app.post('/playtime', (req, res) => {
    try {
        console.log('⏰ Received playtime data:', req.body);

        const data = {
            username: fixUTF8Encoding(req.body.username || 'Playtime Data'),
            content: fixUTF8Encoding(req.body.content || ''),
            timestamp: req.body.timestamp || Date.now(),
            server_name: fixUTF8Encoding(req.body.server_name || 'FiveM Server'),
            type: 'playtime'
        };

        writeLogToFile('playtime', data);

        res.status(200).json({
            success: true,
            message: 'Playtime data received successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Playtime error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint nhận dữ liệu richest
app.post('/richest', (req, res) => {
    try {
        console.log('💰 Received richest data:', req.body);

        const data = {
            username: fixUTF8Encoding(req.body.username || 'Richest Data'),
            content: fixUTF8Encoding(req.body.content || ''),
            timestamp: req.body.timestamp || Date.now(),
            server_name: fixUTF8Encoding(req.body.server_name || 'FiveM Server'),
            type: 'richest'
        };

        writeLogToFile('richest', data);

        res.status(200).json({
            success: true,
            message: 'Richest data received successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Richest error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Hàm fix encoding UTF-8 và Unicode
function fixUTF8Encoding(text) {
    try {
        // Nếu text đã là UTF-8 đúng, trả về nguyên văn
        if (typeof text !== 'string') return text;

        // Fix các ký tự tiếng Việt bị lỗi encoding
        let fixedText = text
            .replace(/Ã¡/g, 'á')
            .replace(/Ã /g, 'à')
            .replace(/áº£/g, 'ả')
            .replace(/Ã£/g, 'ã')
            .replace(/áº¡/g, 'ạ')
            .replace(/Ã©/g, 'é')
            .replace(/Ã¨/g, 'è')
            .replace(/áº»/g, 'ẻ')
            .replace(/áº½/g, 'ẽ')
            .replace(/áº¹/g, 'ẹ')
            .replace(/Ã­/g, 'í')
            .replace(/Ã¬/g, 'ì')
            .replace(/áº¿/g, 'ỉ')
            .replace(/Ä©/g, 'ĩ')
            .replace(/á»‹/g, 'ị')
            .replace(/Ã³/g, 'ó')
            .replace(/Ã²/g, 'ò')
            .replace(/á»/g, 'ỏ')
            .replace(/Ãµ/g, 'õ')
            .replace(/á»/g, 'ọ')
            .replace(/Ãº/g, 'ú')
            .replace(/Ã¹/g, 'ù')
            .replace(/á»§/g, 'ủ')
            .replace(/Å©/g, 'ũ')
            .replace(/á»¥/g, 'ụ')
            .replace(/Ã½/g, 'ý')
            .replace(/á»³/g, 'ỳ')
            .replace(/á»·/g, 'ỷ')
            .replace(/á»¹/g, 'ỹ')
            .replace(/á»±/g, 'ự')
            .replace(/Äƒ/g, 'ă')
            .replace(/Ä'/g, 'đ')
            .replace(/Ã´/g, 'ô')
            .replace(/Æ°/g, 'ư')
            // Uppercase
            .replace(/Ã/g, 'Á')
            .replace(/Ã€/g, 'À')
            .replace(/áº¢/g, 'Ả')
            .replace(/Ãƒ/g, 'Ã')
            .replace(/áº /g, 'Ạ')
            .replace(/Ã‰/g, 'É')
            .replace(/Ãˆ/g, 'È')
            .replace(/áºº/g, 'Ẻ')
            .replace(/áº¼/g, 'Ẽ')
            .replace(/áº¸/g, 'Ẹ')
            .replace(/Ã/g, 'Í')
            .replace(/ÃŒ/g, 'Ì')
            .replace(/áº¾/g, 'Ỉ')
            .replace(/Ä¨/g, 'Ĩ')
            .replace(/á»Š/g, 'Ị')
            .replace(/Ã"/g, 'Ó')
            .replace(/Ã'/g, 'Ò')
            .replace(/á»Ž/g, 'Ỏ')
            .replace(/Ã•/g, 'Õ')
            .replace(/á»Œ/g, 'Ọ')
            .replace(/Ãš/g, 'Ú')
            .replace(/Ã™/g, 'Ù')
            .replace(/á»¦/g, 'Ủ')
            .replace(/Å¨/g, 'Ũ')
            .replace(/á»¤/g, 'Ụ')
            .replace(/Ãœ/g, 'Ý')
            .replace(/á»²/g, 'Ỳ')
            .replace(/á»¶/g, 'Ỷ')
            .replace(/á»¸/g, 'Ỹ')
            .replace(/á»°/g, 'Ự')
            .replace(/Ä‚/g, 'Ă')
            .replace(/Ä/g, 'Đ')
            .replace(/Ã"/g, 'Ô')
            .replace(/Æ¯/g, 'Ư')
            // Các ký tự đặc biệt khác
            .replace(/Tháº¯ng/g, 'Thắng')
            .replace(/ToÃ n/g, 'Toàn')
            .replace(/Ã¢/g, 'â')
            .replace(/Ãª/g, 'ê')
            // Fix Unicode Mathematical Alphanumeric Symbols (fancy text)
            .replace(/𝕯/g, 'D')
            .replace(/𝖎/g, 'i')
            .replace(/𝖊/g, 'e')
            .replace(/𝖒/g, 'm')
            .replace(/𝕼/g, 'Q')
            .replace(/𝖚/g, 'u')
            .replace(/𝖞/g, 'y')
            .replace(/𝖓/g, 'n')
            .replace(/𝖍/g, 'h')
            // Nếu vẫn có ký tự Unicode đặc biệt, thay thế bằng ký tự thường
            .replace(/[\u{1D400}-\u{1D7FF}]/gu, (match) => {
                // Convert Unicode Mathematical symbols to normal characters
                const code = match.codePointAt(0);
                if (code >= 0x1D400 && code <= 0x1D7FF) {
                    // Mathematical Alphanumeric Symbols range
                    const offset = code - 0x1D400;
                    const baseChar = String.fromCharCode(65 + (offset % 26)); // A-Z
                    return baseChar.toLowerCase();
                }
                return match;
            });

        return fixedText;
    } catch (error) {
        console.error('Error fixing UTF-8 encoding:', error);
        return text;
    }
}

// Endpoint nhận dữ liệu top coin
app.post('/topcoin', (req, res) => {
    try {
        console.log('🪙 Received top coin data:', req.body);

        // Fix encoding cho content
        let content = req.body.content || '';
        content = fixUTF8Encoding(content);

        const data = {
            username: req.body.username || 'Top Coin Data',
            content: content,
            timestamp: req.body.timestamp || Date.now(),
            server_name: fixUTF8Encoding(req.body.server_name || 'FiveM Server'),
            type: 'topcoin'
        };

        writeLogToFile('topcoin', data);

        res.status(200).json({
            success: true,
            message: 'Top coin data received successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Top coin error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint để xem logs
app.get('/logs/:type', (req, res) => {
    try {
        const { type } = req.params;
        const logFile = path.join(logsDir, `${type}.json`);
        
        if (!fs.existsSync(logFile)) {
            return res.status(404).json({
                success: false,
                message: 'Log file not found'
            });
        }
        
        const logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
        
        res.json({
            success: true,
            logs: logs.slice(-50), // Trả về 50 logs gần nhất
            total: logs.length
        });
    } catch (error) {
        console.error('❌ Get logs error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Endpoint để xem tất cả logs
app.get('/logs', (req, res) => {
    try {
        const logFiles = fs.readdirSync(logsDir).filter(file => file.endsWith('.json'));
        const allLogs = {};
        
        logFiles.forEach(file => {
            const type = file.replace('.json', '');
            const logFile = path.join(logsDir, file);
            try {
                const logs = JSON.parse(fs.readFileSync(logFile, 'utf8'));
                allLogs[type] = {
                    count: logs.length,
                    latest: logs.slice(-5) // 5 logs gần nhất của mỗi loại
                };
            } catch (error) {
                console.error(`Error reading ${file}:`, error);
            }
        });
        
        res.json({
            success: true,
            logs: allLogs
        });
    } catch (error) {
        console.error('❌ Get all logs error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// FiveM API Proxy endpoints với timeout và error handling
const FIVEM_SERVER = 'http://104.234.180.19:20002';
const FETCH_TIMEOUT = 5000; // 5 seconds timeout

async function fetchWithTimeout(url, timeout = FETCH_TIMEOUT) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'FiveM-Dashboard/1.0'
            }
        });
        clearTimeout(timeoutId);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}

app.get('/api/fivem/info', async (req, res) => {
    try {
        const response = await fetchWithTimeout(`${FIVEM_SERVER}/info.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Error fetching FiveM info:', error.message);
        res.status(503).json({
            error: 'FiveM server unavailable',
            message: 'Cannot connect to FiveM server',
            details: error.message
        });
    }
});

app.get('/api/fivem/dynamic', async (req, res) => {
    try {
        const response = await fetchWithTimeout(`${FIVEM_SERVER}/dynamic.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Error fetching FiveM dynamic:', error.message);
        res.status(503).json({
            error: 'FiveM server unavailable',
            message: 'Cannot connect to FiveM server',
            details: error.message
        });
    }
});

app.get('/api/fivem/players', async (req, res) => {
    try {
        const response = await fetchWithTimeout(`${FIVEM_SERVER}/players.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Error fetching FiveM players:', error.message);
        res.status(503).json({
            error: 'FiveM server unavailable',
            message: 'Cannot connect to FiveM server',
            details: error.message
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// API info endpoint
app.get('/api', (req, res) => {
    res.json({
        success: true,
        message: 'FiveM Data Receiver Server API',
        endpoints: {
            webhook: 'POST /webhook - Nhận webhook chung',
            playtime: 'POST /playtime - Nhận dữ liệu playtime',
            richest: 'POST /richest - Nhận dữ liệu richest',
            logs: 'GET /logs - Xem tất cả logs',
            'logs/:type': 'GET /logs/:type - Xem logs theo loại',
            health: 'GET /health - Health check'
        },
        timestamp: new Date().toISOString()
    });
});

// Root endpoint serves dashboard
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('❌ Server error:', error);
    res.status(500).json({
        success: false,
        error: 'Internal server error'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📡 Webhook endpoint: http://localhost:${PORT}/webhook`);
    console.log(`⏰ Playtime endpoint: http://localhost:${PORT}/playtime`);
    console.log(`💰 Richest endpoint: http://localhost:${PORT}/richest`);
    console.log(`📊 Logs endpoint: http://localhost:${PORT}/logs`);
});
