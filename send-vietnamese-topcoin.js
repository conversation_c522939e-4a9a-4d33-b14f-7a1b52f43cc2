// Script to send Vietnamese top coin data to test encoding fix
const fetch = require('node-fetch');

const testData = {
    username: "Top Coin",
    content: `Top 20 người có nhiều coin nhất:
1. <PERSON><PERSON> (110000146f5dca3) - 29885 coin
2. <PERSON><PERSON> (11000013881584f) - 898 coin
3. <PERSON><PERSON>tp (110000149e05b5e) - 450 coin
4. <PERSON><PERSON><PERSON> (1100001528fc642) - 410 coin
5. <PERSON><PERSON><PERSON> (11000013b95d485) - 170 coin
6. <PERSON><PERSON><PERSON> (1100001171116b1) - 115 coin
7. <PERSON><PERSON><PERSON><PERSON> (steam:110000123456789) - 15000 coin
8. <PERSON><PERSON><PERSON><PERSON> (steam:110000123456790) - 12500 coin
9. <PERSON><PERSON>n <PERSON> (steam:110000123456791) - 10000 coin
10. <PERSON><PERSON><PERSON> (steam:110000123456792) - 8500 coin
11. <PERSON><PERSON><PERSON> (steam:110000123456793) - 7200 coin
12. <PERSON><PERSON> (steam:110000123456794) - 6800 coin
13. Đỗ <PERSON> (steam:110000123456795) - 5900 coin
14. <PERSON><PERSON><PERSON> H (steam:110000123456796) - 5400 coin
15. Đặng Văn I (steam:110000123456797) - 4800 coin
16. Cao Thị J (steam:110000123456798) - 4200 coin
17. <PERSON> Văn K (steam:110000123456799) - 3800 coin
18. Lý Thị L (steam:110000123456800) - 3400 coin
19. Trương Văn M (steam:110000123456801) - 3000 coin
20. Ngô Thị N (steam:110000123456802) - 2600 coin`,
    timestamp: Math.floor(Date.now() / 1000),
    server_name: "Dragon City Server"
};

async function sendVietnameseTopCoin() {
    try {
        console.log('🇻🇳 Sending Vietnamese top coin data...');
        
        const response = await fetch('https://gtav.kaitomc.site/topcoin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Vietnamese top coin data sent successfully!');
            console.log('📊 Response:', result);
            console.log('\n🎯 Test names included:');
            console.log('- Yuki Frost');
            console.log('- Chiu Liêm');
            console.log('- Bạch Béo');
            console.log('- Tài Kays');
            console.log('- Toàn Thắng');
            console.log('- Nguyễn Văn A');
            console.log('- Trần Thị B');
            console.log('- And more Vietnamese names...');
        } else {
            console.log('❌ Failed to send Vietnamese data:', result);
        }
        
    } catch (error) {
        console.error('❌ Error sending Vietnamese data:', error.message);
    }
}

sendVietnameseTopCoin();
