/* Custom styles for FiveM Dashboard */

/* Dark theme colors */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --border-color: #404040;
    --text-primary: #ffffff;
    --text-secondary: #6c757d;
    --accent-blue: #0d6efd;
    --accent-success: #198754;
    --accent-warning: #ffc107;
    --accent-danger: #dc3545;
    --accent-info: #0dcaf0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation for loading */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}

/* Log entry animations */
.log-entry {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.log-entry:hover {
    background-color: #252525 !important;
    border-left-color: var(--accent-blue);
    transform: translateX(5px);
}

/* Stats cards hover effect */
.stats-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Button hover effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Badge styling for log types */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .auto-refresh {
        position: static;
        margin-bottom: 20px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .btn {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .log-entry {
        font-size: 0.9em;
    }
}

/* Custom alert styling */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #f5c6cb;
    border-left: 4px solid var(--accent-danger);
}

/* Navbar enhancements */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5em;
}

.navbar-text {
    font-size: 0.9em;
    color: var(--text-secondary) !important;
}

/* Card enhancements */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Content formatting */
.content {
    max-height: 200px;
    overflow-y: auto;
    background-color: #1a1a1a;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

/* Timestamp styling */
.timestamp {
    font-family: 'Courier New', monospace;
    background-color: rgba(108, 117, 125, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

/* Server name styling */
.server-name {
    background-color: rgba(255, 193, 7, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid var(--accent-warning);
}

/* Loading spinner enhancement */
.fa-spinner {
    animation: fa-spin 1s infinite linear;
}

/* Status indicators */
.text-success {
    color: var(--accent-success) !important;
}

.text-danger {
    color: var(--accent-danger) !important;
}

.text-warning {
    color: var(--accent-warning) !important;
}

.text-info {
    color: var(--accent-info) !important;
}

/* Form controls */
.form-check-input:checked {
    background-color: var(--accent-blue);
    border-color: var(--accent-blue);
}

.form-check-label {
    color: var(--text-primary);
    font-size: 0.9em;
}

/* Empty state styling */
.text-muted {
    color: var(--text-secondary) !important;
}

/* Utility classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Print styles */
@media print {
    .auto-refresh,
    .card:first-child,
    .btn {
        display: none !important;
    }
    
    body {
        background-color: white !important;
        color: black !important;
    }
    
    .card {
        background-color: white !important;
        border: 1px solid #ccc !important;
    }
}
