#!/bin/bash

# Test script to populate dashboard with sample data
BASE_URL="http://localhost:23403"

echo "🧪 Sending test data to dashboard..."

# Test richest data
echo "💰 Sending richest players data..."
curl -X POST "$BASE_URL/richest" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "Top Richest",
    "content": "Top 20 nhiều tiền mặt nhất:\n1. <PERSON><PERSON><PERSON> (steam:***************) - $1500000\n2. Tran <PERSON>hi B (steam:***************) - $1200000\n3. <PERSON> (steam:***************) - $1000000\n4. Pham Thi <PERSON> (steam:***************) - $800000\n5. <PERSON><PERSON> (steam:***************) - $600000\n6. Vu Thi F (steam:***************) - $500000\n7. <PERSON> (steam:***************) - $450000\n8. <PERSON><PERSON><PERSON> (steam:***************) - $400000\n9. <PERSON><PERSON> (steam:***************) - $350000\n10. <PERSON> (steam:***************) - $300000\n\nTop 20 nhiều tiền bank nhất:\n1. Nguyen Van A (steam:***************) - $5000000\n2. Tran Thi B (steam:***************) - $4500000\n3. Le Van C (steam:***************) - $4000000\n4. Pham Thi D (steam:***************) - $3500000\n5. Hoang Van E (steam:***************) - $3000000\n6. Vu Thi F (steam:***************) - $2500000\n7. Do Van G (steam:***************) - $2000000\n8. Bui Thi H (steam:***************) - $1800000\n9. Dang Van I (steam:***************) - $1600000\n10. Cao Thi J (steam:***************) - $1400000",
    "timestamp": '$(date +%s)',
    "server_name": "KaitoMC GTA V Server"
  }'

echo -e "\n"

# Test playtime data
echo "⏰ Sending playtime data..."
curl -X POST "$BASE_URL/playtime" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "Tổng Playtime",
    "content": "Tổng số người: 25\n1. Nguyen Van A (steam:***************) - 156h 23m\n2. Tran Thi B (steam:***************) - 142h 15m\n3. Le Van C (steam:***************) - 128h 45m\n4. Pham Thi D (steam:***************) - 115h 30m\n5. Hoang Van E (steam:***************) - 98h 12m\n6. Vu Thi F (steam:***************) - 87h 45m\n7. Do Van G (steam:***************) - 76h 20m\n8. Bui Thi H (steam:***************) - 65h 35m\n9. Dang Van I (steam:***************) - 54h 18m\n10. Cao Thi J (steam:***************) - 43h 25m\n11. Mai Van K (steam:110000123456799) - 38h 12m\n12. Ly Thi L (steam:110000123456800) - 32h 45m\n13. Truong Van M (steam:110000123456801) - 28h 30m\n14. Ngo Thi N (steam:110000123456802) - 24h 15m\n15. Phan Van O (steam:110000123456803) - 20h 40m\n16. Dinh Thi P (steam:110000123456804) - 18h 25m\n17. Lam Van Q (steam:110000123456805) - 15h 50m\n18. Vo Thi R (steam:110000123456806) - 12h 35m\n19. Ta Van S (steam:110000123456807) - 10h 20m\n20. Duong Thi T (steam:110000123456808) - 8h 45m",
    "timestamp": '$(date +%s)',
    "server_name": "KaitoMC GTA V Server"
  }'

echo -e "\n"

# Test online players data
echo "👥 Sending online players data..."
curl -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "Online Players Since Server Start",
    "content": "Tổng số: 8\n1. Nguyen Van A (steam:***************) - Tổng thời gian: 02:45:30\n2. Tran Thi B (steam:***************) - Tổng thời gian: 01:32:15\n3. Le Van C (steam:***************) - Tổng thời gian: 03:20:45\n4. Pham Thi D (steam:***************) - Tổng thời gian: 00:58:20\n5. Hoang Van E (steam:***************) - Tổng thời gian: 02:15:10\n6. Vu Thi F (steam:***************) - Tổng thời gian: 01:45:35\n7. Do Van G (steam:***************) - Tổng thời gian: 00:42:25\n8. Bui Thi H (steam:***************) - Tổng thời gian: 01:18:50",
    "timestamp": '$(date +%s)',
    "server_name": "KaitoMC GTA V Server"
  }'

echo -e "\n"

# Test general webhook
echo "📡 Sending general webhook data..."
curl -X POST "$BASE_URL/webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "Server Status",
    "content": "Server đang hoạt động bình thường\nSố người chơi online: 8/64\nUptime: 2h 45m\nTPS: 19.8/20.0",
    "timestamp": '$(date +%s)',
    "server_name": "KaitoMC GTA V Server"
  }'

echo -e "\n✅ Test data sent successfully!"
echo "🌐 Open dashboard at: http://localhost:23403"
