// Script to send top coin data with exact names like Top Bank
const fetch = require('node-fetch');

const testData = {
    username: "Top Coin",
    content: `Top 20 người có nhiều coin nhất:
1. <PERSON> (110000158175bab) - 1868600 coin
2. <PERSON><PERSON><PERSON> (1100001171116b1) - 1285100 coin
3. Mc<PERSON> (1100001482ca175) - 1187500 coin
4. 𝕯𝖎ễ𝖒 𝕼𝖚ỳ𝖓𝖍 (11000014f689167) - 1108200 coin
5. <PERSON><PERSON> (110000146f5dca3) - 29885 coin
6. <PERSON><PERSON> (11000013881584f) - 25000 coin
7. Zzz Htp (110000149e05b5e) - 20000 coin
8. <PERSON><PERSON><PERSON><PERSON> (1100001528fc642) - 18000 coin
9. <PERSON><PERSON><PERSON> (11000013b95d485) - 15000 coin
10. <PERSON><PERSON><PERSON><PERSON> (steam:***************) - 12000 coin
11. <PERSON><PERSON><PERSON><PERSON> (steam:***************) - 10000 coin
12. <PERSON><PERSON> (steam:***************) - 9000 coin
13. <PERSON><PERSON><PERSON> (steam:***************) - 8500 coin
14. <PERSON><PERSON><PERSON> (steam:***************) - 7200 coin
15. V<PERSON> Th<PERSON> F (steam:1100001********) - 6800 coin
16. Đỗ Văn G (steam:***************) - 5900 coin
17. Bùi Thị H (steam:***************) - 5400 coin
18. Đặng V<PERSON>n I (steam:***************) - 4800 coin
19. <PERSON> Thị J (steam:***************) - 4200 coin
20. Mai Văn K (steam:***************) - 3800 coin`,
    timestamp: Math.floor(Date.now() / 1000),
    server_name: "Dragon City Server"
};

async function sendExactNamesTopCoin() {
    try {
        console.log('🎯 Sending top coin data with exact names like Top Bank...');
        
        const response = await fetch('https://gtav.kaitomc.site/topcoin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Exact names top coin data sent successfully!');
            console.log('📊 Response:', result);
            console.log('\n🎯 Names should now match Top Bank display:');
            console.log('- Dragon ✓');
            console.log('- Toàn Thắng ✓');
            console.log('- McN ✓');
            console.log('- 𝕯𝖎ễ𝖒 𝕼𝖚ỳ𝖓𝖍 ✓ (special Unicode chars)');
            console.log('- Yuki Frost ✓');
            console.log('- Chiu Liêm ✓');
            console.log('- Bạch Béo ✓');
            console.log('- Tài Kays ✓');
            console.log('\n🔄 Dashboard should now display names correctly!');
        } else {
            console.log('❌ Failed to send exact names data:', result);
        }
        
    } catch (error) {
        console.error('❌ Error sending exact names data:', error.message);
    }
}

sendExactNamesTopCoin();
