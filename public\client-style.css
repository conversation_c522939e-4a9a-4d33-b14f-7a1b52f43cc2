/* GTA V Server Dashboard - Client Style */

:root {
    --primary-color: #f0b90b;
    --secondary-color: #1a1a1a;
    --accent-color: #ff6b35;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;
    --dark-bg: #0a0a0a;
    --card-bg: #1e1e1e;
    --border-color: #333;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', 'Arial Unicode MS', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-feature-settings: "liga" 1, "kern" 1;
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(240, 185, 11, 0.5);
}

.gta-text {
    color: var(--primary-color);
    display: block;
}

.server-text {
    color: var(--accent-color);
    display: block;
    font-size: 0.8em;
}

.server-name {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    color: var(--info-color);
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 500px;
}

.server-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.server-status.online {
    color: var(--success-color);
    border-color: var(--success-color);
    background: rgba(76, 175, 80, 0.1);
}

.server-status.offline {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background: rgba(244, 67, 54, 0.1);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 2rem;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(240, 185, 11, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Navigation */
.main-nav {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-tabs {
    display: flex;
    gap: 10px;
}

.nav-tab {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.nav-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.nav-tab.active {
    background: var(--primary-color);
    color: var(--dark-bg);
    border-color: var(--primary-color);
}

.last-update {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 0.9rem;
}

#refreshIcon.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Main Content */
.main-content {
    padding: 40px 0;
    min-height: 60vh;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.leaderboard-card,
.online-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
    height: 600px;
    display: flex;
    flex-direction: column;
}

.playtime-card,
.topcoin-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
    height: 800px; /* Increased height for more players */
    display: flex;
    flex-direction: column;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--dark-bg);
    padding: 20px;
    font-weight: 600;
}

.card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    flex: 1;
    padding: 0;
    position: relative;
    overflow: hidden;
}

/* Loading Spinner */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    color: var(--text-secondary);
}

.loading-spinner i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

/* Lists */
.leaderboard-list,
.playtime-list,
.online-list,
.topcoin-list {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
}

/* List Header */
.list-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.02);
    margin-bottom: 10px;
    border-radius: 8px;
}

.list-header h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-family: 'Orbitron', monospace;
}

.list-header p {
    margin: 0;
    font-size: 0.9rem;
}

.server-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.stat-item i {
    color: var(--primary-color);
}

.list-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.list-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--primary-color);
    transform: translateX(5px);
}

.rank {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    min-width: 40px;
    text-align: center;
}

.rank.gold { color: #ffd700; }
.rank.silver { color: #c0c0c0; }
.rank.bronze { color: #cd7f32; }

.player-info {
    flex: 1;
    margin-left: 15px;
}

.player-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
    font-family: 'Roboto', 'Segoe UI', 'Arial Unicode MS', 'Noto Sans', sans-serif;
    unicode-bidi: normal;
    word-break: break-word;
    overflow-wrap: break-word;
}

.player-id {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-family: 'Courier New', monospace;
}

.amount {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
}

.cash { color: var(--success-color); }
.bank { color: var(--info-color); }
.playtime { color: var(--warning-color); }
.coin { color: var(--primary-color); }

/* Online Players Specific Styles */
.online-player {
    position: relative;
}

.player-ping {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 2px;
}

.player-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
}

.status-text {
    font-size: 0.9rem;
    color: var(--success-color);
    font-weight: 500;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* Footer */
.main-footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 30px 0;
    margin-top: 50px;
}

.main-footer p {
    margin: 0;
    color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .server-name {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .nav-tab {
        padding: 10px 15px;
        font-size: 0.8rem;
    }
    
    .nav-tab span {
        display: none;
    }
    
    .leaderboard-card,
    .online-card {
        height: 500px;
    }

    .playtime-card,
    .topcoin-card {
        height: 600px;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
