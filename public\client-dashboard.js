// GTA V Server Dashboard - Client JavaScript
class GTAVDashboard {
    constructor() {
        this.baseUrl = window.location.origin;
        this.refreshInterval = null;
        this.currentTab = 'leaderboard';
        this.serverConfig = {
            ip: '**************',
            port: '20002',
            address: '**************:20002'
        };
        this.data = {
            richest: null,
            playtime: null,
            online: null,
            topcoin: null,
            serverInfo: null,
            dynamicInfo: null,
            players: null
        };
        this.init();
    }

    init() {
        this.setupTabs();
        this.checkServerStatus();
        this.loadAllData();
        this.startAutoRefresh();
    }

    setupTabs() {
        const tabs = document.querySelectorAll('.nav-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async checkServerStatus() {
        try {
            const response = await fetch(`${this.baseUrl}/health`);
            const data = await response.json();
            
            const statusElement = document.getElementById('serverStatus');
            if (data.success) {
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i><span>Server đang hoạt động</span>';
                statusElement.className = 'server-status online';
            } else {
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i><span>Server không phản hồi</span>';
                statusElement.className = 'server-status offline';
            }
        } catch (error) {
            const statusElement = document.getElementById('serverStatus');
            statusElement.innerHTML = '<i class="fas fa-times-circle"></i><span>Không thể kết nối</span>';
            statusElement.className = 'server-status offline';
        }
    }

    async loadAllData() {
        this.showRefreshIcon();

        try {
            await Promise.all([
                this.loadRichestData(),
                this.loadPlaytimeData(),
                this.loadTopCoinData(),
                this.loadFiveMServerData(),
                this.loadOnlineData()
            ]);

            this.updateStats();
            this.updateLastUpdate();
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            this.hideRefreshIcon();
        }
    }

    async loadRichestData() {
        try {
            const response = await fetch(`${this.baseUrl}/logs/richest`);
            const data = await response.json();
            
            if (data.success && data.logs.length > 0) {
                const latestLog = data.logs[data.logs.length - 1];
                this.data.richest = this.parseRichestData(latestLog.content);
                this.renderRichestLeaderboard();
            }
        } catch (error) {
            console.error('Error loading richest data:', error);
            this.showError('cashLeaderboard', 'Không thể tải dữ liệu người giàu nhất');
            this.showError('bankLeaderboard', 'Không thể tải dữ liệu người giàu nhất');
        }
    }

    async loadPlaytimeData() {
        try {
            const response = await fetch(`${this.baseUrl}/logs/playtime`);
            const data = await response.json();

            if (data.success && data.logs.length > 0) {
                const latestLog = data.logs[data.logs.length - 1];
                this.data.playtime = this.parsePlaytimeData(latestLog.content);
                this.renderPlaytimeList();
            }
        } catch (error) {
            console.error('Error loading playtime data:', error);
            this.showError('playtimeList', 'Không thể tải dữ liệu thời gian chơi');
        }
    }

    async loadTopCoinData() {
        try {
            const response = await fetch(`${this.baseUrl}/logs/topcoin`);
            const data = await response.json();

            if (data.success && data.logs.length > 0) {
                const latestLog = data.logs[data.logs.length - 1];
                this.data.topcoin = this.parseTopCoinData(latestLog.content);
                this.renderTopCoinList();
            }
        } catch (error) {
            console.error('Error loading top coin data:', error);
            this.showError('topcoinList', 'Không thể tải dữ liệu top coin');
        }
    }

    async loadFiveMServerData() {
        try {
            // Load server info, dynamic info, and players from proxy endpoints
            const [serverInfo, dynamicInfo, players] = await Promise.all([
                this.fetchFiveMAPI(`${this.baseUrl}/api/fivem/info`),
                this.fetchFiveMAPI(`${this.baseUrl}/api/fivem/dynamic`),
                this.fetchFiveMAPI(`${this.baseUrl}/api/fivem/players`)
            ]);

            this.data.serverInfo = serverInfo;
            this.data.dynamicInfo = dynamicInfo;
            this.data.players = players;

            // Process online players data
            if (players && Array.isArray(players)) {
                this.data.online = this.processFiveMPlayers(players);
                this.renderOnlineList();
            }

        } catch (error) {
            console.warn('⚠️ FiveM server unavailable, using fallback data:', error.message);

            // Set default values when FiveM server is unavailable
            this.data.serverInfo = null;
            this.data.dynamicInfo = null;
            this.data.players = [];
            this.data.online = [];

            // Fallback to webhook data
            await this.loadOnlineDataFallback();
        }
    }

    async fetchFiveMAPI(url) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
                mode: 'cors'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Error fetching ${url}:`, error);
            return null;
        }
    }

    async loadOnlineDataFallback() {
        try {
            const response = await fetch(`${this.baseUrl}/logs/webhook`);
            const data = await response.json();

            if (data.success && data.logs.length > 0) {
                // Find the latest online players log
                const onlineLog = data.logs.reverse().find(log =>
                    log.username === 'Online Players Since Server Start'
                );

                if (onlineLog) {
                    this.data.online = this.parseOnlineData(onlineLog.content);
                    this.renderOnlineList();
                }
            }
        } catch (error) {
            console.error('Error loading online data fallback:', error);
            this.showError('onlineList', 'Không thể tải dữ liệu người chơi online');
        }
    }

    async loadOnlineData() {
        // This method is now handled by loadFiveMServerData
        // Keep for backward compatibility
        await this.loadOnlineDataFallback();
    }

    parseRichestData(content) {
        const lines = content.split('\n');
        const cashData = [];
        const bankData = [];
        
        let currentSection = null;
        
        lines.forEach(line => {
            if (line.includes('Top 20 nhiều tiền mặt nhất:')) {
                currentSection = 'cash';
            } else if (line.includes('Top 20 nhiều tiền bank nhất:')) {
                currentSection = 'bank';
            } else if (line.match(/^\d+\./)) {
                const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*-\s*\$(\d+)/);
                if (match) {
                    const [, rank, name, id, amount] = match;
                    const player = {
                        rank: parseInt(rank),
                        name: name.trim(),
                        id: id.trim(),
                        amount: parseInt(amount)
                    };
                    
                    if (currentSection === 'cash') {
                        cashData.push(player);
                    } else if (currentSection === 'bank') {
                        bankData.push(player);
                    }
                }
            }
        });
        
        return { cash: cashData, bank: bankData };
    }

    parsePlaytimeData(content) {
        const lines = content.split('\n');
        const players = [];

        lines.forEach(line => {
            // Match pattern: number. name (id) - hours minutes
            const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*-\s*(\d+)h\s*(\d+)m/);
            if (match) {
                const [, originalRank, name, id, hours, minutes] = match;
                players.push({
                    originalRank: parseInt(originalRank), // Keep original rank for reference
                    name: name.trim(),
                    id: id.trim(),
                    hours: parseInt(hours),
                    minutes: parseInt(minutes),
                    totalMinutes: parseInt(hours) * 60 + parseInt(minutes)
                });
            } else {
                // Try to match incomplete entries (missing time)
                const incompleteMatch = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*$/);
                if (incompleteMatch) {
                    const [, originalRank, name, id] = incompleteMatch;
                    players.push({
                        originalRank: parseInt(originalRank),
                        name: name.trim(),
                        id: id.trim(),
                        hours: 0,
                        minutes: 0,
                        totalMinutes: 0
                    });
                }
            }
        });

        // Sort by total minutes (highest to lowest) - this is the key fix
        players.sort((a, b) => {
            // First sort by total minutes (descending)
            if (b.totalMinutes !== a.totalMinutes) {
                return b.totalMinutes - a.totalMinutes;
            }
            // If same total minutes, sort by hours (descending)
            if (b.hours !== a.hours) {
                return b.hours - a.hours;
            }
            // If same hours, sort by minutes (descending)
            return b.minutes - a.minutes;
        });

        // Re-assign ranks based on sorted order (1 = highest time)
        players.forEach((player, index) => {
            player.rank = index + 1;
        });

        console.log('Sorted playtime data:', players.slice(0, 5)); // Debug log

        return players;
    }

    processFiveMPlayers(playersData) {
        if (!playersData || !Array.isArray(playersData)) {
            return [];
        }

        return playersData.map((player, index) => ({
            rank: index + 1,
            name: player.name || 'Unknown Player',
            id: player.identifiers ? this.extractSteamId(player.identifiers) : 'Unknown ID',
            ping: player.ping || 0,
            endpoint: player.endpoint || 'Unknown',
            time: 'Online now'
        }));
    }

    extractSteamId(identifiers) {
        if (!identifiers || !Array.isArray(identifiers)) {
            return 'Unknown ID';
        }

        const steamId = identifiers.find(id => id.startsWith('steam:'));
        return steamId || identifiers[0] || 'Unknown ID';
    }

    parseTopCoinData(content) {
        const lines = content.split('\n');
        const players = [];

        lines.forEach(line => {
            // Match pattern: number. name (id) - amount coin
            const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*-\s*(\d+)\s*coin/);
            if (match) {
                const [, rank, rawName, id, coins] = match;
                let name = rawName.trim();
                const playerId = id.trim();

                // Chỉ thay thế tên nếu tên giống hệt với ID (không có tên thật)
                if (name === playerId || (name.startsWith('steam:') && name === playerId)) {
                    // Tạo tên dựa trên 4 ký tự cuối của ID
                    const shortId = playerId.slice(-4);
                    name = `Player ${shortId}`;
                }

                // Giữ nguyên tên thật, kể cả có ký tự đặc biệt

                players.push({
                    rank: parseInt(rank),
                    name: name,
                    id: playerId,
                    coins: parseInt(coins)
                });
            }
        });

        // Sort by coins (highest to lowest)
        players.sort((a, b) => b.coins - a.coins);

        // Re-assign ranks
        players.forEach((player, index) => {
            player.rank = index + 1;
        });

        return players;
    }

    parseOnlineData(content) {
        const lines = content.split('\n');
        const players = [];

        lines.forEach(line => {
            const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*-\s*Tổng thời gian:\s*(.+)/);
            if (match) {
                const [, rank, name, id, time] = match;
                players.push({
                    rank: parseInt(rank),
                    name: name.trim(),
                    id: id.trim(),
                    time: time.trim(),
                    ping: null,
                    endpoint: null
                });
            }
        });

        return players;
    }

    renderRichestLeaderboard() {
        if (!this.data.richest) return;
        
        this.hideLoading('cashLoading');
        this.hideLoading('bankLoading');
        
        this.renderLeaderboard('cashLeaderboard', this.data.richest.cash, 'cash');
        this.renderLeaderboard('bankLeaderboard', this.data.richest.bank, 'bank');
    }

    renderLeaderboard(containerId, data, type) {
        const container = document.getElementById(containerId);

        if (!data || data.length === 0) {
            container.innerHTML = '<div class="text-center text-muted p-4">Không có dữ liệu</div>';
            return;
        }

        // Sort by amount (highest to lowest)
        const sortedData = [...data].sort((a, b) => b.amount - a.amount);

        // Re-assign ranks
        sortedData.forEach((player, index) => {
            player.rank = index + 1;
        });

        const typeLabel = type === 'cash' ? 'Tiền Mặt' : 'Tiền Bank';
        const totalCount = sortedData.length;

        const html = `
            <div class="list-header">
                <h4>Top ${typeLabel}: ${totalCount} người chơi</h4>
                <p class="text-muted">Sắp xếp theo số tiền (cao đến thấp)</p>
            </div>
            ${sortedData.map(player => {
                const rankClass = player.rank === 1 ? 'gold' : player.rank === 2 ? 'silver' : player.rank === 3 ? 'bronze' : '';
                return `
                    <div class="list-item">
                        <div class="rank ${rankClass}">#${player.rank}</div>
                        <div class="player-info">
                            <div class="player-name">${this.escapeHtml(player.name)}</div>
                            <div class="player-id">${this.escapeHtml(player.id)}</div>
                        </div>
                        <div class="amount ${type}">$${player.amount.toLocaleString()}</div>
                    </div>
                `;
            }).join('')}
        `;

        container.innerHTML = html;
    }

    renderPlaytimeList() {
        if (!this.data.playtime) return;

        this.hideLoading('playtimeLoading');

        const container = document.getElementById('playtimeList');

        if (this.data.playtime.length === 0) {
            container.innerHTML = '<div class="text-center text-muted p-4">Không có dữ liệu</div>';
            return;
        }

        // Show total count
        const totalCount = this.data.playtime.length;

        const html = `
            <div class="list-header">
                <h4>Tổng số người chơi: ${totalCount}</h4>
                <p class="text-muted">Sắp xếp theo thời gian chơi (cao đến thấp)</p>
            </div>
            ${this.data.playtime.map(player => {
                const rankClass = player.rank === 1 ? 'gold' : player.rank === 2 ? 'silver' : player.rank === 3 ? 'bronze' : '';
                const timeDisplay = player.totalMinutes > 0 ? `${player.hours}h ${player.minutes}m` : 'Chưa có dữ liệu';
                return `
                    <div class="list-item">
                        <div class="rank ${rankClass}">#${player.rank}</div>
                        <div class="player-info">
                            <div class="player-name">${this.escapeHtml(player.name)}</div>
                            <div class="player-id">${this.escapeHtml(player.id)}</div>
                        </div>
                        <div class="amount playtime">${timeDisplay}</div>
                    </div>
                `;
            }).join('')}
        `;

        container.innerHTML = html;
    }

    renderOnlineList() {
        if (!this.data.online) return;

        this.hideLoading('onlineLoading');

        const container = document.getElementById('onlineList');

        if (this.data.online.length === 0) {
            container.innerHTML = '<div class="text-center text-muted p-4">Không có người chơi online</div>';
            return;
        }

        const totalCount = this.data.online.length;
        const maxPlayers = this.data.serverInfo?.vars?.sv_maxclients || 64;
        const serverName = this.data.serverInfo?.hostname || 'KaitoMC Server';

        const html = `
            <div class="list-header">
                <h4>Người chơi online: ${totalCount}/${maxPlayers}</h4>
                <p class="text-muted">Server: ${this.escapeHtml(serverName)}</p>
            </div>
            ${this.data.online.map(player => `
                <div class="list-item online-player">
                    <div class="rank">#${player.rank}</div>
                    <div class="player-info">
                        <div class="player-name">${this.escapeHtml(player.name)}</div>
                        ${player.ping !== null ? `<div class="player-ping">Ping: ${player.ping}ms</div>` : ''}
                    </div>
                    <div class="player-status">
                        <div class="status-indicator online"></div>
                        <div class="status-text">Online</div>
                    </div>
                </div>
            `).join('')}
        `;

        container.innerHTML = html;
    }

    renderTopCoinList() {
        if (!this.data.topcoin) return;

        this.hideLoading('topcoinLoading');

        const container = document.getElementById('topcoinList');

        if (this.data.topcoin.length === 0) {
            container.innerHTML = '<div class="text-center text-muted p-4">Không có dữ liệu top coin</div>';
            return;
        }

        const totalCount = this.data.topcoin.length;

        const html = `
            <div class="list-header">
                <h4>Top Coin: ${totalCount} người chơi</h4>
                <p class="text-muted">Sắp xếp theo số coin (cao đến thấp)</p>
            </div>
            ${this.data.topcoin.map(player => {
                const rankClass = player.rank === 1 ? 'gold' : player.rank === 2 ? 'silver' : player.rank === 3 ? 'bronze' : '';
                return `
                    <div class="list-item">
                        <div class="rank ${rankClass}">#${player.rank}</div>
                        <div class="player-info">
                            <div class="player-name">${this.escapeHtml(player.name)}</div>
                            <div class="player-id">${this.escapeHtml(player.id)}</div>
                        </div>
                        <div class="amount coin">${player.coins.toLocaleString()} coin</div>
                    </div>
                `;
            }).join('')}
        `;

        container.innerHTML = html;
    }

    updateStats() {
        // Update online players count from FiveM API or fallback
        const onlineCount = this.data.online ? this.data.online.length : 0;
        const maxPlayers = this.data.serverInfo?.vars?.sv_maxclients || 128;
        document.getElementById('onlinePlayers').textContent = `${onlineCount}/${maxPlayers}`;

        // Update total players count (from playtime data)
        const totalCount = this.data.playtime ? this.data.playtime.length : 0;
        document.getElementById('totalPlayers').textContent = totalCount;

        // Update total playtime
        if (this.data.playtime && this.data.playtime.length > 0) {
            const totalMinutes = this.data.playtime.reduce((sum, player) => sum + player.totalMinutes, 0);
            const totalHours = Math.floor(totalMinutes / 60);
            document.getElementById('totalPlaytime').textContent = `${totalHours}h`;
        }

        // Update richest amount - prioritize top coin data
        if (this.data.topcoin && this.data.topcoin.length > 0) {
            const topCoinAmount = this.data.topcoin[0].coins;
            document.getElementById('richestAmount').textContent = `${topCoinAmount.toLocaleString()} coin`;
        } else if (this.data.richest && this.data.richest.cash.length > 0) {
            const richestAmount = this.data.richest.cash[0].amount;
            document.getElementById('richestAmount').textContent = `$${richestAmount.toLocaleString()}`;
        }

        // Update server status based on FiveM API response
        this.updateServerStatusFromAPI();
    }

    updateServerStatusFromAPI() {
        const statusElement = document.getElementById('serverStatus');

        if (this.data.serverInfo && this.data.dynamicInfo) {
            const onlineCount = this.data.online ? this.data.online.length : 0;
            const maxPlayers = this.data.serverInfo.vars?.sv_maxclients || 64;
            const serverName = this.data.serverInfo.hostname || 'KaitoMC Server';

            statusElement.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>Server online - ${onlineCount}/${maxPlayers} người chơi</span>
            `;
            statusElement.className = 'server-status online';
        } else if (this.data.serverInfo === null && this.data.dynamicInfo === null) {
            // FiveM server is explicitly unavailable
            statusElement.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <span>FiveM Server không khả dụng - Dashboard vẫn hoạt động</span>
            `;
            statusElement.className = 'server-status warning';
        } else {
            // Fallback to health check
            this.checkServerStatus();
        }
    }

    showError(containerId, message) {
        const container = document.getElementById(containerId);
        container.innerHTML = `<div class="text-center text-danger p-4">${message}</div>`;
    }

    hideLoading(loadingId) {
        const loading = document.getElementById(loadingId);
        if (loading) {
            loading.style.display = 'none';
        }
    }

    showRefreshIcon() {
        const icon = document.getElementById('refreshIcon');
        icon.classList.add('spinning');
    }

    hideRefreshIcon() {
        const icon = document.getElementById('refreshIcon');
        icon.classList.remove('spinning');
    }

    updateLastUpdate() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('vi-VN');
        document.getElementById('lastUpdate').textContent = `Cập nhật lúc ${timeString}`;
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadAllData();
        }, 30000); // 30 seconds
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new GTAVDashboard();
});
