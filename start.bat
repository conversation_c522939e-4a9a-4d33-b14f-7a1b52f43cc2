@echo off
echo Starting FiveM Data Receiver Server...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

REM Create logs directory if it doesn't exist
if not exist "logs" (
    mkdir logs
    echo Created logs directory
)

REM Create public directory if it doesn't exist
if not exist "public" (
    mkdir public
    echo Created public directory
)

echo Starting server...
echo Dashboard will be available at: http://localhost:3000
echo Press Ctrl+C to stop the server
echo.

node server.js

pause
