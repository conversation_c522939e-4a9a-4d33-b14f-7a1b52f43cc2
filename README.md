# FiveM Data Receiver Server & Dashboard

Hệ thống server endpoint và web dashboard để nhận và hiển thị dữ liệu từ FiveM script.

## 🚀 Tính năng

- **Server Endpoints**: Nhận dữ liệu từ FiveM script qua HTTP requests
- **Web Dashboard**: Giao diện web hiện đại để xem logs và thống kê
- **Real-time Updates**: Tự động refresh dữ liệu mỗi 30 giây
- **Log Management**: Lưu trữ và quản lý logs dưới dạng JSON
- **Responsive Design**: Giao diện tương thích với mobile và desktop

## 📋 Yêu cầu hệ thống

- Node.js 14.0.0 trở lên
- NPM hoặc Yarn
- Windows/Linux/macOS

## 🛠️ Cài đặt

### Cách 1: Sử dụng script tự động (Windows)
```bash
# Chạy file start.bat
start.bat
```

### Cách 2: <PERSON>ài đặt thủ công
```bash
# 1. Cài đặt dependencies
npm install

# 2. T<PERSON><PERSON> th<PERSON> mục logs (nếu chưa có)
mkdir logs

# 3. Chạy server
npm start
```

## 🌐 Endpoints

### POST Endpoints (Nhận dữ liệu từ FiveM)

#### `/webhook` - Webhook chung
```json
{
  "username": "Title của message",
  "content": "Nội dung message",
  "timestamp": **********,
  "server_name": "Tên server FiveM"
}
```

#### `/playtime` - Dữ liệu playtime
```json
{
  "username": "Tổng Playtime",
  "content": "Danh sách playtime...",
  "timestamp": **********,
  "server_name": "FiveM Server"
}
```

#### `/richest` - Dữ liệu richest players
```json
{
  "username": "Top Richest",
  "content": "Danh sách richest...",
  "timestamp": **********,
  "server_name": "FiveM Server"
}
```

### GET Endpoints (API)

#### `/` - Web Dashboard
Truy cập giao diện web dashboard

#### `/logs` - Xem tất cả logs
```json
{
  "success": true,
  "logs": {
    "webhook": { "count": 10, "latest": [...] },
    "playtime": { "count": 5, "latest": [...] },
    "richest": { "count": 3, "latest": [...] }
  }
}
```

#### `/logs/:type` - Xem logs theo loại
```json
{
  "success": true,
  "logs": [...],
  "total": 100
}
```

#### `/health` - Health check
```json
{
  "success": true,
  "message": "Server is running",
  "timestamp": "2023-12-07T10:30:00.000Z",
  "uptime": 3600
}
```

## 📊 Web Dashboard

Dashboard cung cấp:

- **Thống kê tổng quan**: Số lượng logs theo từng loại
- **Server status**: Trạng thái hoạt động của server
- **Logs viewer**: Xem logs với filter theo loại
- **Auto refresh**: Tự động cập nhật dữ liệu
- **Responsive design**: Tương thích mobile

### Truy cập Dashboard
```
http://localhost:3000
```

## 🔧 Cấu hình FiveM Script

File `online_log.lua` đã được cập nhật để gửi dữ liệu đến:

```lua
-- Webhook chung
local webhook = "https://gtav.kaitomc.site/webhook"

-- Playtime data
local webhook = "https://gtav.kaitomc.site/playtime"

-- Richest data
local webhook = "https://gtav.kaitomc.site/richest"
```
