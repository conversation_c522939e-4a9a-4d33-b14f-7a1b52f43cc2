// Test script to verify playtime sorting logic
const testData = `Tổng số người: 5
1. <PERSON><PERSON><PERSON><PERSON> (11000010a78f26a) - 24h 49m
2. Atym (110000111d2f8a8) - 52h 49m
3. <PERSON><PERSON><PERSON><PERSON> (11000011255893e) - 48h 50m
4. ti hon (110000114f25666) - 1h 12m
5. <PERSON><PERSON> (110000115d2fe35) - 74h 52m`;

function parsePlaytimeData(content) {
    const lines = content.split('\n');
    const players = [];
    
    lines.forEach(line => {
        // Match pattern: number. name (id) - hours minutes
        const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+?)\)\s*-\s*(\d+)h\s*(\d+)m/);
        if (match) {
            const [, originalRank, name, id, hours, minutes] = match;
            players.push({
                originalRank: parseInt(originalRank),
                name: name.trim(),
                id: id.trim(),
                hours: parseInt(hours),
                minutes: parseInt(minutes),
                totalMinutes: parseInt(hours) * 60 + parseInt(minutes)
            });
        }
    });
    
    console.log('📊 Original data:');
    players.forEach(player => {
        console.log(`   #${player.originalRank} ${player.name} - ${player.hours}h ${player.minutes}m (${player.totalMinutes} total minutes)`);
    });
    
    // Sort by total minutes (highest to lowest)
    players.sort((a, b) => {
        if (b.totalMinutes !== a.totalMinutes) {
            return b.totalMinutes - a.totalMinutes;
        }
        if (b.hours !== a.hours) {
            return b.hours - a.hours;
        }
        return b.minutes - a.minutes;
    });
    
    // Re-assign ranks based on sorted order
    players.forEach((player, index) => {
        player.rank = index + 1;
    });
    
    console.log('\n🏆 Sorted data (correct ranking):');
    players.forEach(player => {
        console.log(`   #${player.rank} ${player.name} - ${player.hours}h ${player.minutes}m (${player.totalMinutes} total minutes)`);
    });
    
    return players;
}

console.log('🧪 Testing playtime sorting logic...\n');
const result = parsePlaytimeData(testData);

console.log('\n✅ Expected result:');
console.log('   #1 should be: Huy (74h 52m = 4492 minutes)');
console.log('   #2 should be: Atym (52h 49m = 3169 minutes)');
console.log('   #3 should be: khoacoii (48h 50m = 2930 minutes)');
console.log('   #4 should be: Tữu\' K (24h 49m = 1489 minutes)');
console.log('   #5 should be: ti hon (1h 12m = 72 minutes)');

console.log('\n🎯 Actual result:');
result.forEach(player => {
    console.log(`   #${player.rank} ${player.name} (${player.totalMinutes} minutes)`);
});
