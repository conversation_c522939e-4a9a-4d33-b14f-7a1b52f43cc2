local onlinePlayersSinceStart = {}
local playerSessionStart = {}

AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local src = source
    local steamID
    for k, v in ipairs(GetPlayerIdentifiers(src)) do
        if string.sub(v, 1, string.len("steam:")) == "steam:" then
            steamID = v
            break
        end
    end
    if steamID then
        -- <PERSON><PERSON><PERSON>, khởi tạo tổng thời gian
        if not onlinePlayersSinceStart[steamID] then
            onlinePlayersSinceStart[steamID] = {name = name, total = 0}
        end
        -- <PERSON><PERSON>u thời điểm vào
        playerSessionStart[steamID] = os.time()
    end
end)

AddEventHandler('playerDropped', function(reason)
    local src = source
    local steamID
    for k, v in ipairs(GetPlayerIdentifiers(src)) do
        if string.sub(v, 1, string.len("steam:")) == "steam:" then
            steamID = v
            break
        end
    end
    if steamID and playerSessionStart[steamID] then
        local sessionTime = os.time() - playerSessionStart[steamID]
        onlinePlayersSinceStart[steamID].total = onlinePlayersSinceStart[steamID].total + sessionTime
        playerSessionStart[steamID] = nil
    end
end)

-- Tự động nhận diện người chơi đang online khi script khởi động
CreateThread(function()
    Wait(1000) -- Đợi server ổn định (1 giây)
    for _, src in ipairs(GetPlayers()) do
        local name = GetPlayerName(src)
        local steamID

        for k, v in ipairs(GetPlayerIdentifiers(src)) do
            if string.sub(v, 1, string.len("steam:")) == "steam:" then
                steamID = v
                break
            end
        end
        if steamID then
            if not onlinePlayersSinceStart[steamID] then
                onlinePlayersSinceStart[steamID] = {name = name, total = 0}
            end
            if not playerSessionStart[steamID] then
                playerSessionStart[steamID] = os.time()
            end
        end
    end
end)

function secondsToHMS(seconds)
    local hours = math.floor(seconds / 3600)
    local mins = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    return string.format("%02d:%02d:%02d", hours, mins, secs)
end

function sendToWebhook(title, message)
    local webhook = "https://gtav.kaitomc.site/webhook" -- URL server webhook mới
    PerformHttpRequest(webhook, function(err, text, headers)
        if err == 200 then
            print("✅ Webhook sent successfully: " .. title)
        else
            print("❌ Webhook failed: " .. tostring(err) .. " - " .. tostring(text))
        end
    end, 'POST', json.encode({
        username = title,
        content = message,
        timestamp = os.time(),
        server_name = GetConvar("sv_hostname", "FiveM Server")
    }), { ['Content-Type'] = 'application/json' })
end

RegisterCommand("sendonlinelog", function(source, args, rawCommand)
    local count = 0
    local list = ""
    for steamID, data in pairs(onlinePlayersSinceStart) do
        -- Nếu người này đang online, cộng thêm thời gian hiện tại
        local total = data.total
        if playerSessionStart[steamID] then
            total = total + (os.time() - playerSessionStart[steamID])
        end
        count = count + 1
        list = list .. count .. ". " .. data.name .. " (" .. steamID .. ") - Tổng thời gian: " .. secondsToHMS(total) .. "\n"
    end
    sendToWebhook("Online Players Since Server Start", "Tổng số: " .. count .. "\n" .. list)
end, true)

-- Hàm gửi playtime tự động
function AutoSendAllPlaytime()
    exports.oxmysql:execute([[
        SELECT
            identifier,
            name,
            created_at,
            last_seen,
            TIMESTAMPDIFF(SECOND, created_at, last_seen) AS play_seconds
        FROM users
        WHERE last_seen != '0000-00-00 00:00:00'
    ]], {}, function(result)
        local webhook = "https://gtav.kaitomc.site/playtime"
        local list = ""
        local count = 0
        local filtered_lines = {}
        for _, row in ipairs(result) do
            if row.play_seconds and row.play_seconds > 3600 then
                local hours = math.floor(row.play_seconds / 3600)
                local mins = math.floor((row.play_seconds % 3600) / 60)
                count = count + 1
                local line = count .. ". " .. (row.name or "unknown") .. " (" .. (row.identifier or "unknown") .. ") - " .. hours .. "h " .. mins .. "m"
                list = list .. line .. "\n"
                table.insert(filtered_lines, line)
            end
        end
        if count == 0 then
            PerformHttpRequest(webhook, function(err, text, headers)
                if err == 200 then
                    print("✅ Playtime webhook sent successfully")
                else
                    print("❌ Playtime webhook failed: " .. tostring(err))
                end
            end, 'POST', json.encode({
                username = "Tổng Playtime",
                content = "Không có ai có dữ liệu hợp lệ!"
            }), { ['Content-Type'] = 'application/json' })
            return
        end
        local members_per_message = 46
        local total_sent = 0
        local part = 1
        local total_lines = #filtered_lines
        while total_sent < total_lines do
            local chunk = ""
            for i = total_sent + 1, math.min(total_sent + members_per_message, total_lines) do
                chunk = chunk .. filtered_lines[i] .. "\n"
            end
            local title = "Tổng Playtime"
            if total_lines > members_per_message then
                title = title .. " (phần " .. part .. ")"
            end
            local content = (total_sent == 0 and ("Tổng số người: " .. count .. "\n") or "") .. chunk
            if #content > 2000 then
                content = content:sub(1, 2000)
            end
            PerformHttpRequest(webhook, function(err, text, headers)
                if err == 200 then
                    print("✅ Playtime webhook sent successfully: " .. title)
                else
                    print("❌ Playtime webhook failed: " .. tostring(err) .. " - " .. tostring(text))
                    -- Gửi thông báo lỗi
                    PerformHttpRequest(webhook, function() end, 'POST', json.encode({
                        username = "Tổng Playtime - Lỗi",
                        content = "Lỗi gửi logs! Mã lỗi: " .. tostring(err) .. "\nNội dung: " .. tostring(text)
                    }), { ['Content-Type'] = 'application/json' })
                end
            end, 'POST', json.encode({
                username = title,
                content = content
            }), { ['Content-Type'] = 'application/json' })
            total_sent = total_sent + members_per_message
            part = part + 1
        end
    end)
end

-- Hàm gửi richest tự động
function AutoSendRichest()
    local webhook = "https://gtav.kaitomc.site/richest"
    exports.oxmysql:execute([[ 
        SELECT identifier, name, accounts FROM users
    ]], {}, function(result)
        local cashList = {}
        local bankList = {}
        for _, row in ipairs(result) do
            if row.accounts then
                local ok, accounts = pcall(json.decode, row.accounts)
                if ok and accounts then
                    local money = tonumber(accounts.money) or 0
                    local bank = tonumber(accounts.bank) or 0
                    table.insert(cashList, {name = row.name or "unknown", identifier = row.identifier or "unknown", money = money})
                    table.insert(bankList, {name = row.name or "unknown", identifier = row.identifier or "unknown", bank = bank})
                end
            end
        end
        table.sort(cashList, function(a, b) return a.money > b.money end)
        table.sort(bankList, function(a, b) return a.bank > b.bank end)
        local cashMsg = "Top 20 nhiều tiền mặt nhất:\n"
        for i = 1, math.min(20, #cashList) do
            local p = cashList[i]
            cashMsg = cashMsg .. i .. ". " .. p.name .. " (" .. p.identifier .. ") - $" .. p.money .. "\n"
        end
        local bankMsg = "Top 20 nhiều tiền bank nhất:\n"
        for i = 1, math.min(20, #bankList) do
            local p = bankList[i]
            bankMsg = bankMsg .. i .. ". " .. p.name .. " (" .. p.identifier .. ") - $" .. p.bank .. "\n"
        end
        local content = cashMsg .. "\n" .. bankMsg
        if #content > 2000 then
            content = content:sub(1, 2000) .. "\n... (danh sách bị cắt bớt)"
        end
        PerformHttpRequest(webhook, function(err, text, headers)
            if err == 200 then
                print("✅ Richest webhook sent successfully")
            else
                print("❌ Richest webhook failed: " .. tostring(err) .. " - " .. tostring(text))
                -- Gửi thông báo lỗi
                PerformHttpRequest(webhook, function() end, 'POST', json.encode({
                    username = "Top Richest - Lỗi",
                    content = "Lỗi gửi logs! Mã lỗi: " .. tostring(err) .. "\nNội dung: " .. tostring(text)
                }), { ['Content-Type'] = 'application/json' })
            end
        end, 'POST', json.encode({
            username = "Top Richest",
            content = content
        }), { ['Content-Type'] = 'application/json' })
    end)
end

-- Tự động gửi logs mỗi 30 phút
CreateThread(function()
    while true do
        AutoSendAllPlaytime()
        AutoSendRichest()
        Wait(1800000) -- 30 phút
    end
end) 