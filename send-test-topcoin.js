// Script to send test top coin data to dashboard
const fetch = require('node-fetch');

const testData = {
    username: "Top Coin",
    content: `Top 20 người có nhiều coin nhất:
1. <PERSON> (steam:110000123456789) - 15000 coin
2. Coin Master (steam:110000123456790) - 12500 coin
3. <PERSON> (steam:110000123456791) - 10000 coin
4. <PERSON> (steam:110000123456792) - 8500 coin
5. Co<PERSON> (steam:110000123456793) - 7200 coin
6. <PERSON> Digger (steam:110000123456794) - 6800 coin
7. <PERSON> King (steam:110000123456795) - 5900 coin
8. Coin Collector (steam:110000123456796) - 5400 coin
9. <PERSON> (steam:110000123456797) - 4800 coin
10. <PERSON> (steam:110000123456798) - 4200 coin
11. Coin Lord (steam:110000123456799) - 3800 coin
12. <PERSON> (steam:110000123456800) - 3400 coin
13. Cash Master (steam:110000123456801) - 3000 coin
14. Coin Pro (steam:110000123456802) - 2600 coin
15. <PERSON> (steam:110000123456803) - 2200 coin
16. <PERSON> Pro (steam:110000123456804) - 1800 coin
17. Coin Expert (steam:110000123456805) - 1500 coin
18. Cash Pro (steam:110000123456806) - 1200 coin
19. Money Fan (steam:110000123456807) - 1000 coin
20. Coin Newbie (steam:110000123456808) - 800 coin`,
    timestamp: Math.floor(Date.now() / 1000),
    server_name: "Dragon City Server"
};

async function sendTestTopCoin() {
    try {
        console.log('🪙 Sending test top coin data...');
        
        const response = await fetch('https://gtav.kaitomc.site/topcoin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Test top coin data sent successfully!');
            console.log('📊 Response:', result);
        } else {
            console.log('❌ Failed to send test data:', result);
        }
        
    } catch (error) {
        console.error('❌ Error sending test data:', error.message);
    }
}

sendTestTopCoin();
